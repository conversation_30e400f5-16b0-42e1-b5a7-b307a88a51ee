import api from '@/lib/api';

export const getObsToken = async (data: any) => {
  return api.get('/v1/toolkit/storage/temporary_credential', {
    params: data
  });
};

export const getDeptUser = async (data: any) => {
  return api.get('/v1/contact/departments', {
    params: data
  });
};

// 获取用户信息
export const getUserInfo = async (id: string) => {
  return api.get('/v1/contact/staff', { params: { id } });
};

// 批量获取用户信息
export const batchGetUserInfo = async (data: any) => {
  return api.post('/v1/contact/staff/batch', data);
};

// 获取用户信息
export const searchTeacher = async (keyword: string) => {
  return api.get('/v1/contact/staff/search', { params: { keyword } });
};

// 任务上报
export const finishTask = async (data: any) => {
  return api.post('/app/v1/point/tasks', data);
};

// 获取账号
export const getAccounts = async () => {
  return api.get('/v1/auth/accounts');
};

// 获取班级列表
export const getClassList = async () => {
  return api.get('/v1/contact/school/departments');
};

// 根据班级获取学生列表
export const getStudentListByClass = async (classId: string) => {
  return api.get('/v1/contact/students/enrollment', {
    params: { classId }
  });
};
