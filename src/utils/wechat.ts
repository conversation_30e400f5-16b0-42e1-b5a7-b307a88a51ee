// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { getSign } from '@/api/wx';
import { getBrowser } from '@/utils';
let wxSdk;
function initWechat() {
  console.log('初始化微信jssdk');
  return new Promise((resolve, reject) => {
    getSign({ url: encodeURIComponent(window.location.href) })
      .then(async (data) => {
        const url = new URL(window.location.href);
        const isDebug =
          url.searchParams.get('debug') === 'true' ||
          process.env.NODE_ENV === 'development';
        wxSdk = (await import('weixin-js-sdk')).default;
        console.log('wxSdk ', await import('weixin-js-sdk'));
        console.log('wxSdk:11 ', wxSdk);
        wxSdk.config({
          debug: isDebug,
          appId: data.appId,
          timestamp: data.timestamp,
          nonceStr: data.nonceStr,
          signature: data.signature,
          jsApiList: [
            'checkJsApi',
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'chooseWXPay',
            'hideMenuItems',
            'hideOptionMenu',
            'chooseImage',
          ],
          openTagList: ['wx-open-launch-app'],
        });
        resolve(1);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

function checkIsReady() {
  return new Promise((resolve, reject) => {
    initWechat().then(() => {
      wxSdk.ready(() => {
        resolve(true);
      });
      wxSdk.error((err: any) => {
        reject(err);
      });
    });
  });
}

function wechatShare(wxData: {
  title: string;
  desc: string;
  link: string;
  imgUrl?: string;
}) {
  const logoUrl = 'https://file.ancda.com/public/file/app/logo.png';
  const shareData = {
    title: wxData.title,
    desc: wxData.desc,
    link: wxData.link.replace(window.location.hash, ''),
    imgUrl: wxData.imgUrl || logoUrl,
  };
  if (getBrowser() === 'wechat') {
    checkIsReady()
      .then(() => {
        console.log('weixin ready share', shareData);
        wxSdk.updateAppMessageShareData({
          title: shareData.title,
          desc: shareData.desc,
          link: shareData.link,
          imgUrl: shareData.imgUrl,
          success: () => {
            console.log('shareData.link', shareData.link);
          },
        });
        wxSdk.updateTimelineShareData({
          title: shareData.title,
          link: shareData.link,
          imgUrl: shareData.imgUrl,
          success: () => {
            console.log('share success');
          },
        });
      })
      .catch((err) => {
        console.log('分享失败', err);
        checkIsReady();
      });
  }
}

function chooseImage() {
  return new Promise((resolve, reject) => {
    checkIsReady()
      .then(() => {
        console.log('打开相册');
        wxSdk.chooseImage({
          count: 1, // 默认9
          sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
          sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
          success(res: any) {
            const localIds = res.localIds; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
            console.log('localIds', localIds);
            resolve(localIds);
          },
        });
      })
      .catch((err) => {
        console.log('打开失败', err);
        reject(err);
      });
  });
}

function weChatInit() {
  checkIsReady()
    .then(() => {
      console.log('微信初始化');
    })
    .catch((err) => {
      console.log('微信初始化失败', err);
      checkIsReady();
    });
}

export { chooseImage, weChatInit, wechatShare };
