'use client';

import React, { useRef, useEffect } from 'react';
import { Volume2 } from 'lucide-react';

interface MediaPlayerProps {
  url: string;
  cover?: string;
  type: number; // 1 for image, 2 for video, 3 for audio
  duration: number;
  name: string;
  onTimeUpdate?: (currentTime: number) => void;
}

const MediaPlayer: React.FC<MediaPlayerProps> = ({
  url,
  cover,
  type,
  duration,
  name,
  onTimeUpdate
}) => {
  const mediaRef = useRef<HTMLVideoElement | HTMLAudioElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const isVideo = type === 2;
  const isAudio = type === 3;
  const isImage = type === 1;

  useEffect(() => {
    const mediaElement = mediaRef.current;

    const handleEnd = () => {
      if (mediaElement) {
        mediaElement.currentTime = 0;
      }
    };

    if (mediaElement) {
      mediaElement.addEventListener('ended', handleEnd);
    }

    return () => {
      if (mediaElement) {
        mediaElement.removeEventListener('ended', handleEnd);
      }
    };
  }, []);

  if (isImage) {
    return (
      <div className="relative bg-black rounded-lg overflow-hidden">
        <img src={url} alt={name} className="w-full h-full object-contain" />
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="relative bg-black rounded-lg overflow-hidden group"
    >
      {isVideo ? (
        <video
          ref={mediaRef as React.RefObject<HTMLVideoElement>}
          src={url}
          poster={cover}
          className="w-full h-full object-contain"
          controls
        />
      ) : (
        <div className="aspect-[16/9] w-full flex items-center justify-center bg-gradient-to-br from-indigo-900 to-purple-900">
          <audio
            ref={mediaRef as React.RefObject<HTMLAudioElement>}
            src={url}
            className="hidden"
          />
          <div className="text-center p-8">
            <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center">
              <Volume2 className="w-12 h-12 text-purple-400" />
            </div>
            <h3 className="text-xl font-medium text-white">{name}</h3>
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaPlayer;
