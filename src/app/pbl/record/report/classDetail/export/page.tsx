'use client';

import { Card, Divider, Grid, List, Space, Tag, Toast } from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Legend,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import { getClassReportDetail, getObservationDimensions } from '@/api/pbl';

import AbilityDistributionGrid from '../components/AbilityDistributionGrid';

interface ReportData {
  classReport: {
    deptName: string;
    startDate: string;
    endDate: string;
    evaluator: string;
    observationCnt: number;
    studentCnt: number;
    radarChart: {
      healthScore: number;
      languageScore: number;
      societyScore: number;
      scienceScore: number;
      artsScore: number;
    };
    deepLearningCounters: {
      communication: number;
      collaboration: number;
      creativity: number;
      criticalThinking: number;
      citizenshipCharacterEducation: number;
    };
    happinessEngagement: {
      happinessAvg: string;
      engagementAvg: string;
    };
  };
  classReportStudentList: Array<{
    studentId: string;
    studentName: string;
    happinessAvg: number;
    engagementAvg: number;
    observationCnt: number;
  }>;
  classReportDomainList: Array<{
    id: number;
    domain: string;
    overview: Array<{
      subDomain: string;
      abilityName: string;
      level1: { pct: string; students: Array<{ id: string; name: string }> };
      level2: { pct: string; students: Array<{ id: string; name: string }> };
      level3: { pct: string; students: Array<{ id: string; name: string }> };
      none: { pct: string; students: Array<{ id: string; name: string }> };
    }>;
    highlights: string;
    issues: string;
    supportCases?: string;
  }>;
  deepLearningStatistics: {
    communication: Array<{
      label: string;
      students: Array<{ id: string; name: string }>;
    }>;
    collaboration: Array<{
      label: string;
      students: Array<{ id: string; name: string }>;
    }>;
    creativity: Array<{
      label: string;
      students: Array<{ id: string; name: string }>;
    }>;
    criticalThinking: Array<{
      label: string;
      students: Array<{ id: string; name: string }>;
    }>;
    citizenshipCharacterEducation: Array<{
      label: string;
      students: Array<{ id: string; name: string }>;
    }>;
  };
}

export default function ClassReportPage() {
  const searchParams = useSearchParams();
  const reportId = searchParams.get('reportId');
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [barChartData, setBarChartData] = useState<
    Array<{
      name: string;
      name1: string;
      name2: string;
      ability: number;
      field: number;
      abilityKey: keyof ReportData['classReport']['deepLearningCounters'];
      fieldKey: keyof ReportData['classReport']['radarChart'];
    }>
  >([]);
  const [fieldRadarChart, setFieldRadarChart] = useState<
    Array<{
      subject: string;
      score: number;
      fullMark: number;
      field: keyof ReportData['classReport']['radarChart'];
    }>
  >([
    { subject: '健康', score: 0, fullMark: 100, field: 'healthScore' },
    { subject: '语言', score: 0, fullMark: 100, field: 'languageScore' },
    { subject: '社会', score: 0, fullMark: 100, field: 'societyScore' },
    { subject: '科学', score: 0, fullMark: 100, field: 'scienceScore' },
    { subject: '艺术', score: 0, fullMark: 100, field: 'artsScore' },
  ]);
  const [dimensions, setDimensions] = useState<string[]>([]);
  const [dimensionsList] = useState([
    {
      dimensions: '2',
      label: '1. 《3-6岁儿童学习与发展指南》',
      value: '评估指标包括：5个领域，12个子领域，共36项评估指标。',
    },
    {
      dimensions: '3',
      label: '2. 《幸福感和参与度指标》',
      value: '评估指标包括：2个领域，10个子领域，共10项评估指标。',
    },
    {
      dimensions: '1',
      label: '3. 《深度学习能力指标》',
      value: '评估指标包括：5个领域，12个子领域，共36项评估指标。',
    },
  ]);
  const [happinessAvgObj, setHappinessAvgObj] = useState<
    Record<string, string[]>
  >({
    '1': [],
    '2': [],
    '3': [],
    '4': [],
    '5': [],
  });
  const [engagementAvgObj, setEngagementAvgObj] = useState<
    Record<string, string[]>
  >({
    '1': [],
    '2': [],
    '3': [],
    '4': [],
    '5': [],
  });
  const abilityCounts = {
    communication: '沟通能力',
    collaboration: '协作能力',
    creativity: '创造力',
    criticalThinking: '批判性思维',
    citizenshipCharacterEducation: '公民意识与品格教育',
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!reportId) {
        setError('报告ID不能为空');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const resReportData = await getClassReportDetail({ reportId });

        if (!resReportData) {
          throw new Error('获取报告数据失败');
        }

        // Validate required data
        if (
          !resReportData.classReport ||
          !resReportData.classReportStudentList ||
          !resReportData.classReportDomainList
        ) {
          throw new Error('报告数据格式不正确');
        }

        const fieldRadarChartTemp = [
          {
            subject: '健康',
            score: 0,
            fullMark: 100,
            field: 'healthScore' as const,
          },
          {
            subject: '语言',
            score: 0,
            fullMark: 100,
            field: 'languageScore' as const,
          },
          {
            subject: '社会',
            score: 0,
            fullMark: 100,
            field: 'societyScore' as const,
          },
          {
            subject: '科学',
            score: 0,
            fullMark: 100,
            field: 'scienceScore' as const,
          },
          {
            subject: '艺术',
            score: 0,
            fullMark: 100,
            field: 'artsScore' as const,
          },
        ].map((item) => ({
          ...item,
          score: resReportData.classReport?.radarChart[item.field] || 0,
        }));
        setFieldRadarChart(fieldRadarChartTemp);

        // Prepare bar chart data
        const dataBar: Array<{
          name: string;
          name1: string;
          name2: string;
          ability: number;
          field: number;
          abilityKey: keyof ReportData['classReport']['deepLearningCounters'];
          fieldKey: keyof ReportData['classReport']['radarChart'];
        }> = [
          {
            name: '创造力/科学',
            name1: '创造力',
            name2: '科学',
            ability: 0,
            field: 0,
            abilityKey: 'creativity',
            fieldKey: 'scienceScore',
          },
          {
            name: '协作能力/社会',
            name1: '协作能力',
            name2: '社会',
            ability: 0,
            field: 0,
            abilityKey: 'collaboration',
            fieldKey: 'societyScore',
          },
          {
            name: '批判性思维/艺术',
            name1: '批判性思维',
            name2: '艺术',
            ability: 0,
            field: 0,
            abilityKey: 'criticalThinking',
            fieldKey: 'artsScore',
          },
          {
            name: '沟通能力/语言',
            name1: '沟通能力',
            name2: '语言',
            ability: 0,
            field: 0,
            abilityKey: 'communication',
            fieldKey: 'languageScore',
          },
          {
            name: '公民意识品格教育/健康',
            name1: '公民意识品格教育',
            name2: '健康',
            ability: 0,
            field: 0,
            abilityKey: 'citizenshipCharacterEducation',
            fieldKey: 'healthScore',
          },
        ];

        for (const item of dataBar) {
          item.ability =
            resReportData.classReport.deepLearningCounters[item.abilityKey];
          item.field = resReportData.classReport.radarChart[item.fieldKey];
        }

        // Process happiness and engagement data
        const happinessAvgObjTemp: Record<string, string[]> = {
          '1': [],
          '2': [],
          '3': [],
          '4': [],
          '5': [],
        };
        const engagementAvgObjTemp: Record<string, string[]> = {
          '1': [],
          '2': [],
          '3': [],
          '4': [],
          '5': [],
        };

        for (const student of resReportData.classReportStudentList) {
          const happinessLevel = Math.round(student.happinessAvg).toString();
          const engagementLevel = Math.round(student.engagementAvg).toString();

          if (happinessLevel in happinessAvgObjTemp) {
            happinessAvgObjTemp[happinessLevel]?.push(student.studentName);
          }
          if (engagementLevel in engagementAvgObjTemp) {
            engagementAvgObjTemp[engagementLevel]?.push(student.studentName);
          }
        }

        setHappinessAvgObj(happinessAvgObjTemp);
        setEngagementAvgObj(engagementAvgObjTemp);
        setBarChartData(dataBar);
        setReportData(resReportData);
      } catch (error) {
        console.error('Failed to fetch report data:', error);
        setError(error instanceof Error ? error.message : '获取报告数据失败');
        Toast.show({
          content: '获取报告数据失败，请稍后重试',
          position: 'center',
        });
      } finally {
        setLoading(false);
      }
    };
    getObservationDimensions().then((res: any) => {
      setDimensions(res.dimensions);
    });
    fetchData();
  }, [reportId]);

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-lg text-gray-600">加载中...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-lg text-red-600">{error}</div>
          <button
            type="button"
            className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
            onClick={() => window.location.reload()}
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-lg text-gray-600">暂无报告数据</div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen w-[8.5in] bg-white px-6 print:px-0"
      data-loaded={loading}
    >
      <div className="mb-8 text-center">
        <h1 className="mb-2 text-2xl font-bold text-gray-900">
          {reportData.classReport?.deptName || '未知班级'} 幼儿评价报告
        </h1>
      </div>
      <Card>
        <Grid columns={2} gap={12}>
          <Grid.Item>
            <div className="flex flex-row items-center">
              <p className="text-sm text-gray-500">报告范围：</p>
              <p className="text-sm text-gray-500">
                {reportData.classReport?.startDate || '-'}-
                {reportData.classReport?.endDate || '-'}
              </p>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex flex-row items-center">
              <p className="text-sm text-gray-500">评估人：</p>
              <p className="text-sm text-gray-500">
                {reportData.classReport?.evaluator || '-'}
              </p>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex flex-row items-center">
              <p className="text-sm text-gray-500">评估班级：</p>
              <p className="text-sm text-gray-500">
                {reportData.classReport?.deptName || '-'}
              </p>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex flex-row items-center">
              <p className="text-sm text-gray-500">评估幼儿人数：</p>
              <p className="text-sm text-gray-500">
                {reportData.classReport?.studentCnt || 0}
              </p>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex flex-row items-center">
              <p className="text-sm text-gray-500">观察记录条数：</p>
              <p className="text-sm text-gray-500">
                {reportData.classReport?.observationCnt || 0}
              </p>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex flex-row items-center">
              <p className="text-sm text-gray-500">评估内容项：</p>
              <p className="text-sm text-gray-500">
                <Space wrap style={{ '--gap': '8px' }}>
                  <Tag color="primary" fill="solid">
                    3-6岁指南
                  </Tag>
                  <Tag color="primary" fill="solid">
                    幸福感与参与度
                  </Tag>
                  <Tag color="primary" fill="solid">
                    深度学习能力
                  </Tag>
                </Space>
              </p>
            </div>
          </Grid.Item>
        </Grid>
      </Card>

      <Card>
        <h2 className="mb-4 border-b border-gray-200 pb-2 text-xl font-bold text-gray-900">
          报告说明
        </h2>
        <p
          style={{ color: '#666', fontSize: '0.9em', margin: '0 0 10px 16px' }}
        >
          本报告依据以下标准进行评估：
        </p>
        <List
          style={{
            '--border-inner': 'none',
            '--border-top': 'none',
            '--border-bottom': 'none',
          }}
        >
          {dimensionsList.map((item) => {
            if (dimensions.includes(item.dimensions)) {
              return (
                <List.Item
                  key={item.dimensions}
                  description={
                    <span style={{ color: '#888', fontSize: '0.85em' }}>
                      {item.value}
                    </span>
                  }
                >
                  {item.label}
                </List.Item>
              );
            }
          })}
        </List>
      </Card>

      <Card>
        <h2 className="mb-4 border-b border-gray-200 pb-2 text-xl font-bold text-gray-900">
          班级发展情况
        </h2>
        <div className="mb-2 rounded-lg bg-white p-4">
          <h3 className="mb-4 text-lg font-semibold text-gray-800">
            3-6岁儿童学习与发展指南
          </h3>
          <div style={{ height: '300px', width: '100%' }}>
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart data={fieldRadarChart} outerRadius="80%">
                <PolarGrid stroke="#e5e7eb" />
                <PolarAngleAxis
                  dataKey="subject"
                  tick={{ fill: '#666', fontSize: 16 }}
                />
                <PolarRadiusAxis
                  angle={18}
                  domain={[0, 100]}
                  tick={{ fill: '#999', fontSize: 10 }}
                />
                <Radar
                  name="当前表现"
                  dataKey="score"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.4}
                />
                <Legend />
              </RadarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="mb-2 rounded-lg bg-white p-4">
          <h3 className="mb-4 text-lg font-semibold text-gray-800">
            幸福感和参与度指标
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="rounded-lg bg-white p-4">
              <h4 className="mb-2 text-sm font-medium text-gray-500">幸福感</h4>
              <p className="text-2xl font-bold text-blue-600">
                {reportData.classReport.happinessEngagement.happinessAvg}
                /5分
              </p>
            </div>
            <div className="rounded-lg bg-white p-4">
              <h4 className="mb-2 text-sm font-medium text-gray-500">参与度</h4>
              <p className="text-2xl font-bold text-blue-600">
                {reportData.classReport.happinessEngagement.engagementAvg}
                /5分
              </p>
            </div>
          </div>
        </div>
        {dimensions.includes('1') && (
          <div className="mb-2 rounded-lg bg-white p-4">
            <h3 className="mb-4 text-lg font-semibold text-gray-800">
              项目式学习中幼儿深度学习能力表
            </h3>
            <AbilityDistributionGrid
              abilityDistribution={Object.entries(
                reportData.deepLearningStatistics,
              ).map(([key, value]) => ({
                name: abilityCounts[key as keyof typeof abilityCounts],
                distribution: value.map((item) => ({
                  category: item.label,
                  students: item.students.map((s) => s.name),
                })),
              }))}
            />
          </div>
        )}
        <Divider style={{ margin: '20px 0', color: '#999' }}>
          各领域发展评估详细说明
        </Divider>
        {reportData.classReportDomainList.map((item) => (
          <Card
            key={item.id}
            title={`${item.domain}领域`}
            style={{
              marginBottom: '12px',
              borderRadius: '6px',
            }}
            headerStyle={{
              fontSize: '1em',
              color: '#1677ff',
              borderBottom: '1px solid #eee',
            }}
            bodyStyle={{ padding: '10px 12px' }}
          >
            {item.overview.map((domain, index) => (
              <div className="mb-4 space-y-4" key={`${item.id}-${index}`}>
                <p className="text-base font-semibold">
                  {index + 1}、{domain.subDomain}|{domain.abilityName}
                </p>
                <ul className="ml-2 space-y-2">
                  <li className="flex flex-row items-center">
                    <span>班级整体发展水平分布：</span>
                    <Space wrap style={{ '--gap': '6px' }}>
                      {Number(domain.level1.pct) > 0 && (
                        <Tag color="warning">{domain.level1.pct}%水平1 </Tag>
                      )}
                      {Number(domain.level2.pct) > 0 && (
                        <Tag color="success">{domain.level2.pct}%水平2 </Tag>
                      )}
                      {Number(domain.level3.pct) > 0 && (
                        <Tag color="success">{domain.level3.pct}%水平3</Tag>
                      )}
                      {Number(domain.none.pct) > 0 && (
                        <Tag color="danger"> {domain.none.pct}%未观察到</Tag>
                      )}
                    </Space>
                  </li>
                </ul>
              </div>
            ))}
            {/* 用线条区分 */}
            <Divider style={{ margin: '15px 15px', color: '#999' }} />
            <ul>
              <li className="mb-2">
                <span className="text-base">发展亮点：</span>
                <span style={{ color: '#555' }}>{item.highlights}</span>
              </li>
              <li className="mb-2">
                <span className="text-base">突出问题：</span>
                <span style={{ color: '#ff4d4f' }}>{item.issues}</span>
              </li>
              {item.supportCases && (
                <li className="mb-2">
                  <span className="text-base">建议：</span>
                  <span style={{ color: '#555' }}>{item.supportCases}</span>
                </li>
              )}
            </ul>
          </Card>
        ))}
        <Divider style={{ margin: '15px 0', color: '#999' }}>
          幸福感指标详情
        </Divider>
        {Object.entries(happinessAvgObj).map(([level, students]) => (
          <div key={`happiness-${level}`} className="mb-2">
            <p className="mb-2">{`幸福感 - 等级 ${level}`}</p>
            <Space wrap style={{ '--gap': '6px' }}>
              {students.length > 0 ? (
                students.map((student) => (
                  <Tag key={student} color="warning" fill="outline">
                    {student}
                  </Tag>
                ))
              ) : (
                <Tag color="weak" fill="outline">
                  无
                </Tag>
              )}
            </Space>
          </div>
        ))}
        <Divider style={{ margin: '15px 0', color: '#999' }}>
          参与度指标详情
        </Divider>
        {Object.entries(engagementAvgObj).map(([level, students]) => (
          <div key={`engagement-${level}`} className="mb-2">
            <p className="mb-2">{`参与度 - 等级 ${level}`}</p>
            <Space wrap style={{ '--gap': '6px' }}>
              {students.length > 0 ? (
                students.map((student) => (
                  <Tag key={student} color="warning" fill="outline">
                    {student}
                  </Tag>
                ))
              ) : (
                <Tag color="weak" fill="outline">
                  无
                </Tag>
              )}
            </Space>
          </div>
        ))}
      </Card>

      <Card>
        <h2 className="mb-4 border-b border-gray-200 pb-2 text-xl font-bold text-gray-900">
          班级幼儿观察记录统计
        </h2>
        <div className="mb-4">
          <div style={{ height: '400px', width: '100%' }}>
            {barChartData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={barChartData}
                  margin={{
                    top: 60,
                    right: 30,
                    left: 20,
                    bottom: 40,
                  }}
                  barGap={35}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey=""
                    tick={{ fontSize: 12 }}
                    interval={0}
                    height={60}
                  />
                  <YAxis />
                  <Tooltip />
                  <Legend
                    layout="horizontal"
                    verticalAlign="top"
                    align="center"
                    wrapperStyle={{ paddingBottom: 12 }}
                  />
                  {dimensions.includes('1') && (
                    <Bar
                      name="能力"
                      dataKey="ability"
                      fill="#4F8AFA"
                      radius={[10, 10, 0, 0]}
                      maxBarSize={60}
                      label={({ x, y, width, height, value, index }) => {
                        const data = barChartData[index];
                        if (!data || data.name1 == null || value == null)
                          return <g />;
                        return (
                          <g>
                            <text
                              x={x + width / 2}
                              y={y - 25}
                              textAnchor="middle"
                              fill="#333"
                              fontSize={11}
                            >
                              {data.name1}
                            </text>
                            <text
                              x={x + width / 2}
                              y={y - 10}
                              textAnchor="middle"
                              fill="#4F8AFA"
                              fontSize={12}
                              fontWeight="bold"
                            >
                              {value}
                            </text>
                          </g>
                        );
                      }}
                    />
                  )}
                  {dimensions.includes('2') && (
                    <Bar
                      name="领域"
                      dataKey="field"
                      fill="#FFB74D"
                      radius={[10, 10, 0, 0]}
                      maxBarSize={60}
                      label={({ x, y, width, height, value, index }) => {
                        const data = barChartData[index];
                        if (!data || data.name2 == null || value == null)
                          return <g />;
                        return (
                          <g>
                            <text
                              x={x + width / 2}
                              y={y - 45}
                              textAnchor="middle"
                              fill="#333"
                              fontSize={11}
                            >
                              {data.name2}
                            </text>
                            <text
                              x={x + width / 2}
                              y={y - 30}
                              textAnchor="middle"
                              fill="#FFB74D"
                              fontSize={12}
                              fontWeight="bold"
                            >
                              {value}
                            </text>
                          </g>
                        );
                      }}
                    />
                  )}
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex h-full items-center justify-center text-gray-500">
                暂无数据
              </div>
            )}
          </div>
        </div>

        <Divider style={{ margin: '15px 0', color: '#999' }}>
          幼儿观察记录次数
        </Divider>
        {reportData.classReportStudentList?.length > 0 ? (
          <Grid columns={8} gap={1}>
            {reportData.classReportStudentList
              ?.sort((a, b) => b.observationCnt - a.observationCnt)
              ?.map((student) => (
                <Grid.Item key={student.studentId}>
                  <Card
                    bodyStyle={{
                      padding: '10px 5px',
                      textAlign: 'center',
                      borderRadius: '4px',
                      backgroundColor: '#fff',
                    }}
                  >
                    <div
                      style={{
                        fontSize: '0.9em',
                        color: '#333',
                        marginBottom: '5px',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {student.studentName}
                    </div>
                    <Tag
                      color="processing"
                      fill="outline"
                      style={{ fontSize: '0.8em' }}
                    >
                      {student.observationCnt}次
                    </Tag>
                  </Card>
                </Grid.Item>
              ))}
          </Grid>
        ) : (
          <div className="text-center text-gray-500">暂无学生数据</div>
        )}
      </Card>
    </div>
  );
}
