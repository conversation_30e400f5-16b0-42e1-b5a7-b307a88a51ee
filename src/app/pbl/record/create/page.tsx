'use client';

import clsx from 'clsx';
import { useEffect, useRef, useState, createRef } from 'react';
import { Save, Loader, Calendar } from 'lucide-react';

import Evaluation, { type EvaluationRef } from './components/Evaluation';
import StudentPicker from '@/components/StudentPicker';
import Upload, { type UploadRef, type FileType } from '@/components/UploadFile';

import { createObservation, getProjectList } from '@/api/pbl';
import { useRouter, useSearchParams } from 'next/navigation';
import { Toast, DatePicker, Picker } from 'antd-mobile';
import { useImmer } from 'use-immer';
import { format } from 'date-fns';
import { evaluationAtom } from '@/store/pbl';
import { useAtom } from 'jotai';
import SelectField from '../../material/create/components/SelectField';

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
  classId?: string;
  abilities: Ability[];
}

interface MediaItem {
  type: 1 | 2;
  url: string;
  fileSize: number;
  cover: string;
  duration: number;
  videoPlayType: number;
}

interface Ability {
  id?: string;
  name?: string;
  icon?: React.ComponentType<{ className?: string }>;
  color?: 'blue' | 'purple' | 'green' | 'yellow' | 'red';
  abilityId?: string | number;
}

interface StudentEvaluation {
  studentId: string;
  studentName?: string;
  avatar?: string;
  gender?: number;
  deptId?: string;
  abilities: Ability[];
}

interface FormData {
  title: string;
  content: string;
  type: number;
  source: number;
  date: string;
  deptId: string;
  projectId: string;
  projectName: string;
  medias: MediaItem[];
  studentEvaluations: StudentEvaluation[];
}

export default function App() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const classId = searchParams?.get('classId');
  const [formData, setFormData] = useImmer<FormData>({
    title: '',
    content: '',
    type: 1,
    source: 2,
    deptId: classId || '',
    projectId: '',
    projectName: '',
    medias: [],
    date: format(new Date(), 'yyyy-MM-dd'),
    studentEvaluations: []
  });

  const [evaluationRefs, setEvaluationRefs] = useState<
    Record<string, React.RefObject<EvaluationRef>>
  >({});
  const [selectedStudents, setSelectedStudents] = useState<Student[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [evaluations, setEvaluation] = useAtom(evaluationAtom);
  const [projectList, setProjectList] = useState<
    Array<{ projectId: string; projectName: string; deptId: string }>
  >([]);

  const [projectPickerVisible, setProjectPickerVisible] = useState(false);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '创建观察记录';
    }
    // 清空 atom 数据
    setEvaluation({});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setEvaluation]);

  useEffect(() => {
    fetchProjectList();
  }, []);

  useEffect(() => {
    // 当学生列表变化时，为每个学生创建对应的 ref
    const newRefs: Record<string, React.RefObject<EvaluationRef>> = {};
    for (const student of formData.studentEvaluations) {
      newRefs[student.studentId] = createRef<EvaluationRef>();
    }
    setEvaluationRefs(newRefs);
  }, [formData.studentEvaluations]);

  const fetchProjectList = async () => {
    const response = await getProjectList({});
    // @ts-ignore
    const data = response.list || [];

    setProjectList(
      data.filter((item: { deptId: string }) => item.deptId === classId)
    );
  };

  const handleSubmit = () => {
    const currentFormData = JSON.parse(JSON.stringify(formData));

    // 转换评估数据为提交格式
    // currentFormData.studentEvaluations = Object.entries(allStudentEvaluations)
    //   .filter(([_, abilities]) => {
    //     // 检查是否有至少一个值为 true 的能力
    //     return Object.values(abilities).some((value) => value === true);
    //   })
    //   .map(([studentId, abilities]) => {
    //     // 只保留值为 true 的 ability
    //     const filteredAbilities = Object.entries(abilities)
    //       .filter(([_, value]) => value === true)
    //       .map(([abilityId]) => ({
    //         abilityId: Number.parseInt(abilityId)
    //       }));

    //     return {
    //       studentId,
    //       deptId: classId,
    //       abilities: filteredAbilities
    //     };
    //   });

    currentFormData.studentEvaluations = Object.entries(evaluations)
      .map(([studentId, abilitiesObj]) => {
        // 只保留 abilityId 为 34 并且为 true 的
        const abilities = Object.entries(abilitiesObj)
          .filter(([_, value]) => value === true)
          .map(([abilityId]) => ({ abilityId: Number(abilityId) }));

        // 如果 abilities 为空则不返回
        if (abilities.length === 0) return null;

        return {
          studentId,
          deptId: classId,
          abilities
        };
      })
      .filter(Boolean); // 去除空项

    // 输出处理后的数据以便调试
    console.log('处理后的学生评估数据：', currentFormData.studentEvaluations);

    if (uploadRef.current) {
      const files = uploadRef.current.getFiles();
      console.log('当前文件列表：', files);
      currentFormData.medias = files.map((item) => ({
        ...item,
        cover:
          item.type === 'video'
            ? `${item.url}?x-workflow-graph-name=video-thumbnail`
            : '',
        name: item.name || '未命名文件',
        fileSize: item.size || 0,
        type:
          item.type === 'image'
            ? 1
            : item.type === 'video'
              ? 2
              : item.type === 'audio'
                ? 3
                : 0
      }));
    }

    // 使用最新的数据进行后续操作
    if (
      !currentFormData.content ||
      !currentFormData.date ||
      !currentFormData.studentEvaluations.length
    ) {
      Toast.show({
        icon: 'fail',
        content: '请填写完整信息'
      });
      return;
    }
    setIsSubmitting(true);

    createObservation(currentFormData)
      .then((res) => {
        console.log(res);
        Toast.show({
          icon: 'success',
          content: '保存成功'
        });
        router.back();
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  // 定义 StudentPicker 返回的学生类型 (不含 abilities)
  type PickerStudent = Omit<Student, 'abilities'>;

  // 调整 handleStudentsSelect 的参数类型
  const handleStudentsSelect = (students: PickerStudent[]) => {
    // 将 PickerStudent[] 映射为 Student[]，添加空的 abilities
    const fullStudents: Student[] = students.map((s) => ({
      ...s,
      abilities: [] // 添加空的 abilities 数组
    }));
    setSelectedStudents(fullStudents); // 使用映射后的完整类型

    setFormData((draft) => {
      // draft.studentEvaluations 需要 studentId, studentName, avatar 等
      draft.studentEvaluations = students.map((student: PickerStudent) => ({
        studentId: student.studentId,
        deptId: student.classId,
        studentName: student.studentName,
        avatar: student.avatar, // 确保 avatar 存在
        gender: student.gender, // 确保 gender 存在
        abilities: [] // 初始化为空数组
      }));
    });
  };
  // Removed duplicate lines 218-222

  const uploadRef = useRef<UploadRef>(null);

  // 获取文件列表
  const handleGetFiles = () => {
    if (uploadRef.current) {
      const files = uploadRef.current.getFiles();
      console.log('当前文件列表：', files);
    }
  };

  // 清空文件列表
  const handleClearFiles = () => {
    uploadRef.current?.clearFiles();
  };

  return (
    <main className="">
      <div className="p-4 pb-0">
        <StudentPicker
          // onMultiSelect 现在接收 PickerStudent[] 类型
          onMultiSelect={handleStudentsSelect}
          // multiValue 可能也需要调整类型，暂时保持不变
          multiValue={selectedStudents}
          placeholder="请选择班级和学生"
          multiple={true}
          classId={classId ?? undefined}
        />
      </div>
      <div className="flex-1 overflow-y-auto p-4">
        <div className="fade-in">
          <div className="mb-0">
            <label
              htmlFor="observation"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              观察记录描述
            </label>
            <textarea
              id="observation"
              name="observation"
              value={formData.content}
              onChange={(e) =>
                setFormData((draft) => {
                  draft.content = e.target.value;
                })
              }
              placeholder="记录下您观察到的儿童行为、语言或互动情况..."
              rows={6}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            />
          </div>

          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
            <span>已输入 {formData.content.length} 字</span>
            <span>最多 500 字</span>
          </div>
          <div className="mb-4">
            <label
              htmlFor="date"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              记录日期
            </label>
            <div
              id="date"
              onClick={() => setDatePickerVisible(true)}
              className="w-full p-2 border border-gray-300 rounded-md flex items-center justify-between text-gray-700"
            >
              {formData.date}
              <Calendar className="w-4 h-4" />
            </div>
          </div>
        </div>
        {projectList.length > 0 && (
          <div className="mb-4">
            <SelectField
              label="关联 PBL 项目"
              value={formData.projectName || ''}
              placeholder="请选择 PBL 项目"
              onClick={() => setProjectPickerVisible(true)}
            />
          </div>
        )}
        <div className="fade-in mb-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">
            关联能力
            <span className="text-xs text-gray-500 ml-2">
              (每个学生可单独选择)
            </span>
          </h3>
          {formData.studentEvaluations.length === 0 ? (
            <div className="text-center py-4 text-gray-500">请先选择学生</div>
          ) : (
            <div className="space-y-6">
              {formData.studentEvaluations.map((child, index) => (
                <div
                  key={child.studentId || index}
                  className="border rounded-lg p-3"
                >
                  <div className="flex items-center mb-3">
                    <img
                      src={child.avatar}
                      alt={child.studentName}
                      className="w-8 h-8 rounded-full mr-2"
                    />
                    <span className="font-medium">{child.studentName}</span>
                    {/* <span className="ml-2 text-sm text-gray-500">
                      ({getChildAbilityCount(index)}/{abilityList.length})
                    </span> */}
                  </div>
                  {/* 传递 studentId 给 Evaluation 组件 */}
                  <Evaluation
                    ref={evaluationRefs[child.studentId]}
                    studentId={child.studentId}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="fade-in">
          <label
            htmlFor="observation"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            关联文件
          </label>
          <Upload
            ref={uploadRef}
            initialFiles={formData.medias as unknown as FileType[]}
            className="min-h-[400px]"
          />
        </div>
      </div>

      <div className="p-4 border-t border-gray-200 bg-white">
        <button
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.content}
          className={clsx(
            'w-full py-3 rounded-md flex items-center justify-center gap-2',
            isSubmitting || !formData.content
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-indigo-500 text-white hover:bg-primary/90'
          )}
        >
          {isSubmitting ? (
            <>
              <Loader className="w-4 h-4 animate-spin" />
              提交中...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              保存观察记录
            </>
          )}
        </button>
      </div>
      <DatePicker
        title="时间选择"
        visible={datePickerVisible}
        onClose={() => {
          setDatePickerVisible(false);
        }}
        max={new Date()}
        onConfirm={(val) => {
          setFormData((draft) => {
            draft.date = format(val, 'yyyy-MM-dd');
          });
        }}
      />
      {/* PBL 项目选择 */}
      <Picker
        columns={[
          projectList.map((project) => ({
            label: project.projectName,
            value: project.projectId
          }))
        ]}
        visible={projectPickerVisible}
        onClose={() => {
          setProjectPickerVisible(false);
        }}
        onConfirm={(v, c) => {
          if (v[0]) {
            setFormData((draft) => {
              draft.projectId = (c.items[0]?.value as string) || '';
              draft.projectName = (c.items[0]?.label as string) || '';
            });
          }
        }}
      />
    </main>
  );
}
