'use client';

import { BookOpenCheck, FileText, Compass, Library } from 'lucide-react';
import { clsx } from 'clsx';
import { useRouter } from 'next/navigation';
import { getObservationClassList } from '@/api/pbl';
import { useQuery } from '@tanstack/react-query';

// 定义班级数据类型
interface ClassItem {
  id: string;
  name: string;
  evaluatedStudentCnt: number;
  studentCnt: number;
}

// 定义 API 响应类型
interface ApiResponse {
  list: ClassItem[];
  total?: number;
  page?: number;
  size?: number;
}

export default function App() {
  if (typeof document !== 'undefined') {
    document.title = '观察记录';
  }
  const router = useRouter();

  // 使用 react-query 获取班级列表数据
  const { data, isLoading, error } = useQuery<ApiResponse>({
    queryKey: ['observationClassList'],
    queryFn: () =>
      getObservationClassList({}) as unknown as Promise<ApiResponse>
  });

  // 获取班级列表，如果数据不存在则提供空数组作为默认值
  const classes = data?.list || [];

  return (
    <main className=" bg-slate-50 ">
      <div className="p-4">
        <div className="flex justify-between items-center mb-4 px-2">
          <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-emerald-100'
              }
            >
              <BookOpenCheck className="size-5 text-emerald-500" />
            </div>
            <span className="text-xs text-gray-700">PBL 教学</span>
          </div>
          <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/record/report');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-orange-100'
              }
            >
              <FileText className="w-5 h-5 text-orange-500" />
            </div>
            <span className="text-xs text-gray-700">报告管理</span>
          </div>
          <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/material?isShowPblTaskMenu=true');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-indigo-100'
              }
            >
              <Library className="w-5 h-5 text-indigo-500" />
            </div>
            <span className="text-xs text-gray-700">素材管理</span>
          </div>
          <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/record/guide');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-purple-100'
              }
            >
              <Compass className="w-5 h-5 text-purple-500" />
            </div>
            <span className="text-xs text-gray-700">观察指引</span>
          </div>
        </div>
        <div className="">
          {isLoading ? (
            <div className="text-center py-10">加载中...</div>
          ) : error ? (
            <div className="text-center py-10 text-red-500">
              加载失败，请稍后重试
            </div>
          ) : classes.length === 0 ? (
            <div className="text-center py-10 text-gray-500">暂无班级数据</div>
          ) : (
            classes.map((classItem: ClassItem) => (
              <div
                key={classItem.id}
                onClick={() => {
                  router.push(
                    `/pbl/record/class?id=${classItem.id}&name=${classItem.name}&isShowPblTaskMenu=true`
                  );
                }}
                className="card-hover mb-4 cursor-pointer rounded-xl bg-white px-4 py-5 shadow-sm"
              >
                <div className="flex items-start justify-between">
                  <div>
                    <h2 className="text-xl font-semibold">{classItem.name}</h2>
                  </div>
                </div>
                {classItem.evaluatedStudentCnt === 0 ? (
                  <div className="mt-3 text-gray-500">还没有记录</div>
                ) : (
                  <div className="mt-3">
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-sm">记录人数</span>
                      <span className="text-sm font-medium">
                        {classItem.evaluatedStudentCnt}/{classItem.studentCnt}
                      </span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-gray-100">
                      <div
                        className={clsx(
                          'progress-bar h-2 rounded-full bg-emerald-500 overflow-hidden'
                        )}
                        style={{
                          width: `${Math.min((classItem.evaluatedStudentCnt / classItem.studentCnt) * 100, 100)}%`
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </main>
  );
}
