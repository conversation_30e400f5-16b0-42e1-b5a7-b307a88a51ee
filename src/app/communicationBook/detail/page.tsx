import axios from "axios";

import styles from "./page.module.scss";

export const metadata = {
  title: "家园联系册",
};

export default async function Page({ params, searchParams }) {
  const { periodId, studentId, instId, periodName } = searchParams;
  const url = `${process.env.NEXT_APP_ADMIN_API_HOST}/admin/v1/commBook/records-detail`;
  console.log("url", url);
  const res = await axios.get(url, {
    params: {
      periodId,
      studentId,
      instId,
    },
  });
  const posts = res.data;
  console.log(posts);

  return (
    <div>
      <table className={styles.table}>
        <tbody>
          <tr>
            <td colSpan={4} className={styles.tableTitle}>
              {periodName}
            </td>
          </tr>
          <tr>
            <td className={styles.tableTd}>班级</td>
            <td className="w-1/4">{posts?.[0]?.class_name || ""}</td>
            <td className={styles.tableTd}>姓名</td>
            <td className="w-1/4">{posts?.[0]?.student_name || ""}</td>
          </tr>
          {posts.map((item, index) => (
            <>
              <tr>
                <td colSpan={4} className="font-bold">
                  {item.behave_type === 1 ? "在园表现" : "在家表现"}
                </td>
              </tr>
              {item.behave_list.map((childItem, childIndex) => (
                <tr key={`behave${childIndex}`}>
                  <td colSpan={3} className="bg-gray-50 text-left">
                    {childItem.name}
                  </td>
                  <td>
                    {"★★★★★☆☆☆☆☆".slice(
                      5 - childItem.score,
                      10 - childItem.score,
                    )}
                  </td>
                </tr>
              ))}

              <tr>
                <td className="bg-gray-50">
                  {item.behave_type === 1 ? "老师评语" : "家长评语"}
                </td>
                <td colSpan={3}>
                  <div className="text-left">
                    {item.content || "还没有点评"}
                  </div>
                  {Array.isArray(item.imgs) && item.imgs.length > 0 && (
                    <div className="flex flex-wrap">
                      {item.imgs.map((imgItem, imgIndex) => (
                        <img
                          key={`img${imgIndex}`}
                          src={`${imgItem}?x-image-process=image/resize,m_fill,w_150,h_150`}
                          alt=""
                          className="mb-2 mr-2 h-[150px] w-[150px]"
                        />
                      ))}
                    </div>
                  )}
                  {(item.teacher_name || item.parent_name) && (
                    <div className="flex justify-end">
                      --{item.teacher_name || item.parent_name || ""}
                    </div>
                  )}
                </td>
              </tr>
            </>
          ))}
        </tbody>
      </table>
    </div>
  );
}
