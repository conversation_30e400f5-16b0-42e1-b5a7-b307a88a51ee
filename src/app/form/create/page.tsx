'use client';

import {
  restrictToVerticalAxis,
  restrictToWindowEdges,
} from '@dnd-kit/modifiers';
import { verticalListSortingStrategy } from '@dnd-kit/sortable';
import { NavBar, Popup, Tabs } from 'antd-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import Script from 'next/script';
import process from 'process';
import React, { useEffect, useState } from 'react';

import { getFormDetail, getTemplate } from '@/api/form';
import { PiPlusCircle } from '@/components/Icons';
import { defaultAvatar, departmentAvatar } from '@/constant/config';
import { useFormStore } from '@/store/useFormStore';
import { nanoid, postMessage } from '@/utils';

import { Sortable } from '../../workflow/create/components';
import { properties } from '../../workflow/create/components/data';
import WidgetSetting from '../../workflow/create/components/WidgetSetting';
import WidgetsList from '../../workflow/create/components/WidgetsList';
import BaseForm from './components/BaseForm';
import SettingForm from './components/SettingForm';
import Submit from './components/Submit';

function convertMinutesToArray(minutes: number) {
  if (minutes === 0) return [0, 0, 0, 0];
  const days = Math.floor(minutes / 1440);
  const hours = Math.floor((minutes % 1440) / 60);
  const mins = minutes % 60;

  return [0, days, hours, mins];
}

export default function Index() {
  const searchParams = useSearchParams();
  const instanceId = searchParams?.get('instanceId');
  const templateId = searchParams?.get('templateId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }

  const forms = useFormStore((state) => state.forms);
  const addFormItem = useFormStore((state) => state.addFormItem);
  const setBaseForm = useFormStore((state) => state.setBaseForm);
  const setForm = useFormStore((state) => state.setForm);
  const setSettingForm = useFormStore((state) => state.setSettingForm);
  const removeFormItem = useFormStore((state) => state.removeFormItem);
  const widgetId = useFormStore((state) => state.widgetId);
  const setWidgetId = useFormStore((state) => state.setWidgetId);
  const updateFormItem = useFormStore((state) => state.updateFormItem);

  const [activeIndex, setActiveIndex] = useState('1');
  const [formItemPopupVisible, setFormItemPopupVisible] = useState(false);

  useEffect(() => {
    if (forms.length === 0) {
      // 编辑数据
      if (instanceId) {
        getFormDetail(instanceId).then((res: any) => {
          const {
            instanceName,
            iconUrl,
            remark,
            cateId,
            form,
            cycleType,
            isRepeat,
            isHoliday,
            cycleTime,
            scope,
            endTime,
            remindTime,
          } = res;
          setBaseForm({
            instanceName,
            iconUrl,
            remark,
            cateId,
          });
          if (form) {
            const { properties } = JSON.parse(form);
            const formSchema = Object.keys(properties).map((key) => ({
              ...properties[key],
              id: key,
            }));
            setForm(formSchema.sort((a, b) => a.order - b.order));
          }
          // 设置表单设置
          const isCustom =
            cycleType !== 0 ||
            isRepeat !== 0 ||
            isHoliday !== 0 ||
            endTime !== 0 ||
            remindTime !== 0;
          const settings = {
            cycleType,
            cycleTime: JSON.parse(cycleTime).map((item) => String(item)),
            isRepeat,
            isHoliday,
            endTime,
            remindTime: convertMinutesToArray(remindTime),
            isCustom,
            scope: scope.map((item) => ({
              id: item.objId,
              name: item.objName || '-',
              avatar: item.objType === 2 ? defaultAvatar : departmentAvatar,
              type: item.objType === 2 ? 'user' : 'dept',
            })),
          };
          setSettingForm(settings);
        });
      } else if (templateId) {
        // 从模板创建
        getTemplate(templateId).then((res: any) => {
          const { name, iconUrl, remark, form } = res;
          setBaseForm({
            instanceName: name,
            iconUrl,
            remark,
            cateId: '', // 需清空分组
          });
          if (form) {
            try {
              const { properties } = JSON.parse(form);
              const formSchema = Object.keys(properties).map((key) => ({
                ...properties[key],
                id: key,
              }));
              setForm(formSchema.sort((a, b) => a.order - b.order));
            } catch (error) {}
          }
        });
      } else {
        // 初始数据
        // 测试全部表单类型取消下面注释
        // setForm(formItems);
        setForm([
          {
            id: `form-${nanoid(12)}`,
            ...(properties.input || {}),
          },
        ]);
      }
    }
  }, []);

  return (
    <div className="flex h-screen flex-col bg-[#F7F9FF]">
      <div className="fixed top-0 z-10 w-full bg-white">
        <NavBar
          onBack={() => {
            postMessage({ goBack: 1 });
          }}
        >
          <span className="font-bold">
            {instanceId ? '修改表单' : '创建表单'}
          </span>
        </NavBar>
        <Tabs
          activeLineMode="fixed"
          onChange={(key) => {
            setActiveIndex(key);
          }}
          style={{
            '--fixed-active-line-width': '30px',
            '--content-padding': '0',
            '--active-line-height': '4px',
            '--active-line-color':
              'linear-gradient(90deg, #31C3FF 0%, #4E78FF 100%)',
            '--active-line-border-radius': '2px',
          }}
        >
          <Tabs.Tab className="w-1/2" forceRender key="1" title="基础信息" />
          <Tabs.Tab className="w-1/2" forceRender key="2" title="设置" />
        </Tabs>
      </div>

      <div className="flex flex-1 flex-col overflow-scroll pt-[180px]">
        {activeIndex === '1' && (
          <div className="flex flex-1 flex-col bg-[#F7F9FF] ">
            <BaseForm />
            <div className="px-4 text-base text-stone-400">表单信息</div>
            <Sortable
              forms={forms}
              handle
              items={forms}
              modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
              removable
              removeFormItem={removeFormItem}
              setForm={setForm}
              setWidgetId={setWidgetId}
              strategy={verticalListSortingStrategy}
              // renderItem={renderItem}
            />
            <button
              className="mb-4 flex items-center justify-center"
              onClick={() => {
                setFormItemPopupVisible(true);
              }}
              type="button"
            >
              <PiPlusCircle
                fontSize={24}
                style={{ color: 'var(--adm-color-primary)' }}
              />
              <span className="primary-color ml-1 text-base">添加控件</span>
            </button>
          </div>
        )}
        {activeIndex === '2' && <SettingForm instanceId={instanceId || ''} />}
      </div>

      <Submit instanceId={instanceId || ''} templateId={templateId || '0'} />

      <Popup
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          backgroundColor: '#fff',
        }}
        onMaskClick={() => {
          setFormItemPopupVisible(false);
        }}
        visible={formItemPopupVisible}
      >
        <div className=" mt-3 text-center text-lg">控件库</div>
        <WidgetsList
          onSelect={(type: string) => {
            setFormItemPopupVisible(false);
            addFormItem({
              id: `form-${nanoid(12)}`,
              ...(properties[type] || {}),
            });
          }}
        />
      </Popup>
      <Popup
        bodyStyle={{ width: '100vw' }}
        position="right"
        visible={!!widgetId}
      >
        <WidgetSetting
          forms={forms}
          setWidgetId={setWidgetId}
          updateFormItem={updateFormItem}
          widgetId={widgetId}
        />
      </Popup>
      {process.env.NODE_ENV === 'development' && (
        <Script
          onLoad={() => {
            const { eruda } = window as any;
            eruda.init();
            eruda.position({ x: 20, y: 240 });
          }}
          src="//cdn.jsdelivr.net/npm/eruda"
        />
      )}
    </div>
  );
}
