import axios from 'axios';
import { redirect } from 'next/navigation';

export default async function Page({ searchParams }) {
  console.log('searchParams', searchParams);
  const { studentId, instId, userId } = searchParams;

  const url = `${process.env.NEXT_APP_API_HOST}/app/v1/mall/points-url`;

  const response = await axios.get(url, {
    params: {
      studentId,
      instId,
      userId,
    },
  });
  if (!response.data) {
    throw new Error('Failed to fetch redirect URL');
  }
  const { point_mall_url } = response.data;
  console.log('point_mall_url', point_mall_url);
  if (!point_mall_url) {
    throw new Error('Failed to fetch redirect URL');
  }
  redirect(point_mall_url);
}
