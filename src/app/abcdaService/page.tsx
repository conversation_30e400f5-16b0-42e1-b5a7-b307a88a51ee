'use client';

import YSF from '@neysf/qiyu-web-sdk';
import { SpinLoading } from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
// 掌心会员客服
const AbcdaService = () => {
  const searchParams = useSearchParams() as any;
  const [mobile] = useState(searchParams.get('mobile') || '');
  const [name] = useState(searchParams.get('name') || '');
  const [uid] = useState(searchParams.get('uid') || '');
  const [templateId] = useState(searchParams.get('templateId') || '');
  const ysfInstance = useRef<any>();

  const initYSF = async () => {
    if (!ysfInstance.current) {
      ysfInstance.current = await YSF.init(
        `5486e9261b85af09ba48accfc5f4dcaf`,
        {
          hidden: 1,
        },
        null,
      );
    }
    if (ysfInstance.current) {
      ysfInstance.current('config', {
        uid,
        name,
        mobile,
        success() {
          handleService();
          console.log('信息上报成功');
        },
        error() {
          console.log('信息上报失败');
        },
      });
    }
  };
  useEffect(() => {
    initYSF();
  }, [mobile, name, uid]);
  const handleService = () => {
    // ysfInstance.current('open', {
    //   templateId,
    // });
    window.location.href = `${ysfInstance.current(
      'url',
    )}&templateId=${templateId}`;
  };
  return (
    <div className="flex min-h-screen flex-col bg-white">
      <div className="fixed inset-0 z-50 flex size-full items-center justify-center">
        <SpinLoading color="#999" />
      </div>
    </div>
  );
};
export default AbcdaService;
