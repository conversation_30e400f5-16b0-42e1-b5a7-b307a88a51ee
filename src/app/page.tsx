import { headers } from 'next/headers';
import * as React from 'react';

import Layout from '@/components/layout/Layout';
import { isPalmBaby } from '@/lib/utils';

export default function HomePage() {
  const headersList = headers();
  const hostname = headersList.get('host') || '';

  const isPalmBabyApp = isPalmBaby(hostname);
  return (
    <Layout>
      <main>
        <section className="bg-white">
          <div className="layout relative flex min-h-screen flex-col items-center justify-center py-12 text-center">
            <h1 className="inline-block bg-gradient-to-r from-blue-600 via-green-500 to-indigo-400 bg-clip-text text-transparent">
              {isPalmBabyApp ? '掌心宝贝' : '掌心智校'}
            </h1>
          </div>
        </section>
      </main>
    </Layout>
  );
}
