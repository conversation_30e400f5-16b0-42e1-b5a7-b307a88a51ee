'use client';

import { Image, ImageViewer } from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getClockInList, getTaskDetailInfo } from '@/api/babyTask';
import FooterQrCode from '@/components/FooterQrcode';
import { wechatShare } from '@/utils/wechat';

import styles from './taskDetail.module.css';

export const dynamic = 'force-dynamic';

const Page = () => {
  const searchParams = useSearchParams();
  const taskId = searchParams?.get('taskId') || null;
  const [taskInfo, setTaskInfo] = useState<any>();
  const [checkInList, setCheckInList] = useState<any>([]);
  const [endDays, setEndDays] = useState<any>();

  const [taskDescriptionImg, setTaskDescriptionImg] = useState<any>();
  useEffect(() => {
    if (taskId) {
      getDetail();
      getCheckList();
    }
  }, [taskId]);
  const getCheckList = () => {
    getClockInList({ taskId, type: 1 }).then((res: any) => {
      setCheckInList(res.data?.clockinList);
    });
  };
  const getDetail = () => {
    getTaskDetailInfo({ taskId }).then((res: any) => {
      const wxData = {
        title: taskInfo?.title,
        desc: taskInfo?.content,
        link: window.location.href,
        imgUrl: taskInfo?.cover || '',
      };
      wechatShare(wxData);
      setTaskInfo(res.data);
      countdown(res.data.startDate, res.data.endDate);
      setTaskDescriptionImg(
        res.data.resource.find((i: any) => i.resourceType === 1)?.url,
      );
    });
  };
  const countdown = (startDate: string, endDate: string) => {
    const now = new Date().getTime();
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();
    // 距离结束时间
    const nowToEndDays = end - now;
    setEndDays(formatSecToStr(nowToEndDays));
  };
  // 参数：毫秒
  const formatSecToStr = (seconds: number) => {
    const daySec = 24 * 60 * 60 * 1000;
    const hourSec = 60 * 60 * 1000;
    const minuteSec = 60 * 1000;
    const dd = Math.round(seconds / daySec);
    const hh = Math.round((seconds % daySec) / hourSec);
    const mm = Math.round((seconds % hourSec) / minuteSec);
    const ss = seconds % minuteSec;
    if (dd > 0) {
      return `${dd}天后截止`;
    }
    if (hh > 0) {
      return `${hh}小时后截止`;
    }
    if (mm > 0) {
      return `${mm}分钟后截止`;
    }
    if (ss > 0) {
      return `${ss}秒后截止`;
    }
    return '已结束';
  };

  const showImage = (photos: [], index: number) => {
    ImageViewer.Multi.show({
      images: photos.map((i: any) => i.url),
      defaultIndex: index,
    });
  };

  return (
    <div className="flex min-h-screen flex-col">
      <div className={styles.container}>
        <div className={styles.bodyContent}>
          <div className={styles.taskTitledec}>
            <div className={styles.leftTitle}>
              <div
                className={styles.title}
                style={{ fontSize: '18px', marginBottom: '10px' }}
              >
                {taskInfo?.title}
              </div>
              <div className={styles.subRow}>
                <div className={(styles.subfont, styles.subnomfont)}>
                  {endDays}
                </div>
                {taskInfo?.clockinPeopleNum !== 0 && (
                  <div className={styles.subnomfont}>
                    {taskInfo?.clockinPeopleNum}人参与
                  </div>
                )}
              </div>
              <div className={styles.subRow}>
                <div className={(styles.subfont, styles.subsmfont)}>
                  {taskInfo?.teacherName}
                </div>
                <div className={styles.subsmfont}>{taskInfo?.createTime}</div>
              </div>
            </div>
            <div className={styles.rightImg}>
              <img className={styles.cover} src={taskInfo?.cover} />
            </div>
          </div>
        </div>
        <div className={styles.otherContent}>
          <div className={styles.taskdec}>
            <div className={styles.declabel}>任务介绍</div>
            <div className={styles.decdetail}>
              <div className={styles.decdetailinfobox}>
                <div className={styles.decdetailinfo}>
                  {taskInfo?.content.length > 70
                    ? `${taskInfo?.content.slice(0, 70)}...`
                    : taskInfo?.content}
                </div>
              </div>
              {!!taskDescriptionImg && (
                <div className={styles.decdetailimg}>
                  <img className={styles.cover} src={taskDescriptionImg} />
                </div>
              )}
            </div>
            {/* <div className={styles.goDetail} onClick={()=>goDetail}>详情>></div> */}
          </div>
          {checkInList?.map((item: any, index: number) => {
            return (
              <div className={styles.taskSub} key={index}>
                <div className={styles.subheader}>
                  <div className={styles.userinfo}>
                    <img className={styles.userHeader} src={item.avatar} />
                    <div className={styles.userName}>{item.studentName}</div>
                  </div>
                  <div className={styles.taskNumber}>
                    第{item.clockinTimes}/{item.needClockinNum}次
                  </div>
                </div>
                <div className={styles.taskDetail}>
                  {item.content.length > 50
                    ? `${item.content.slice(0, 50)}...`
                    : item.content}
                </div>
                <div className={styles.centerImage}>
                  {item.clockinResource
                    .filter((i: any) => i.resourceType === 1)
                    .map((imgItem: any, index: number) => {
                      return (
                        <div className={styles.imageSubclass} key={index}>
                          <Image
                            onClick={() =>
                              showImage(
                                item.clockinResource.filter(
                                  (i: any) => i.resourceType === 1,
                                ),
                                index,
                              )
                            }
                            src={imgItem.url}
                            alt=""
                          />
                        </div>
                      );
                    })}
                </div>
                {item.clockinResource
                  .filter((i: any) => i.resourceType === 2)
                  .map((videoItem: any, index: number) => {
                    return (
                      <div className={styles.centerVideo} key={index}>
                        <video
                          src={videoItem.url}
                          className={styles.video}
                          controls
                          width="100%"
                          height="auto"
                          poster={`${videoItem.url}?x-workflow-graph-name=video-thumbnail`}
                        >
                          你的浏览器不支持
                        </video>
                      </div>
                    );
                  })}
                {item.clockinResource
                  .filter((i: any) => i.resourceType === 3)
                  .map((audioItem: any, index: number) => {
                    return (
                      <div className={styles.centerAudio} key={index}>
                        <audio
                          src={audioItem.url}
                          controls
                          className={styles.audio}
                        >
                          你的浏览器不支持
                        </audio>
                      </div>
                    );
                  })}
              </div>
            );
          })}
        </div>
      </div>
      <FooterQrCode />
    </div>
  );
};
export default Page;
