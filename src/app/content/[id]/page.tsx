import Image from 'next/image';

import { getPostData } from '@/lib/posts';
import { isPalmBaby } from '@/lib/utils';
import { headers } from 'next/headers';

interface Props {
  params: { id: string };
}

export async function generateMetadata({ params }: Props) {
  const postData = await getPostData(params.id);
  return {
    title: postData.title || '帮助中心'
  };
}

export default async function Post({ params }: Props) {
  const headersList = headers();
  const hostname = headersList.get('host') || '';
  const isPalmBabyApp = isPalmBaby(hostname);
  const postData = await getPostData(params.id);

  let dataString = JSON.stringify(postData);

  if (isPalmBabyApp) {
    dataString = dataString.replaceAll('掌心智校', '掌心宝贝');
  }
  let data;

  try {
    data = JSON.parse(dataString);
  } catch (error) {
    console.error('Error parsing post data:', error);
    return <div>Error loading post.</div>;
  }

  return (
    <div className="relative flex min-h-screen flex-col  overflow-hidden bg-gray-50 lg:py-12">
      <Image
        src="/images/beams.jpg"
        alt=""
        className="fixed left-1/2 top-48 max-w-none -translate-x-2/3 -translate-y-1/2"
        width="1308"
        height="942"
      />
      <div className="absolute inset-0 bg-top [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))] lg:bg-[url(/images/grid.svg)]" />
      <div className="relative w-full  bg-white p-6 md:mx-auto md:max-w-3xl lg:max-w-4xl lg:pb-28 lg:pt-16">
        <div className="mx-auto max-w-prose lg:text-lg">
          <h2 className="mb-4">{data.title}</h2>
          <article
            dangerouslySetInnerHTML={{ __html: data.contentHtml }}
            className="prose md:prose-lg lg:prose-xl"
          />
        </div>
      </div>
    </div>
  );
}
