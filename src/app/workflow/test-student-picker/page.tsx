'use client';

import React, { useState } from 'react';
import { Button, NavBar } from 'antd-mobile';
import StudentPickerWidget from '../create/components/widgets/StudentPicker';

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
  classId?: string;
}

export default function TestStudentPicker() {
  const [selectedStudents, setSelectedStudents] = useState<Student[]>([]);
  const [readOnly, setReadOnly] = useState(false);

  const handleStudentChange = (students: Student[]) => {
    console.log('Selected students:', students);
    setSelectedStudents(students);
  };

  const toggleReadOnly = () => {
    setReadOnly(!readOnly);
  };

  const clearSelection = () => {
    setSelectedStudents([]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <NavBar>学生选择器测试</NavBar>

      <div className="p-4 space-y-4">
        <div className="bg-white rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4">学生选择器组件测试</h3>

          <div className="mb-4">
            <StudentPickerWidget
              value={selectedStudents}
              onChange={handleStudentChange}
              readOnly={readOnly}
              placeholder="请选择参与工作流程的学生"
            />
          </div>

          <div className="flex gap-2 mb-4">
            <Button size="small" color="primary" onClick={toggleReadOnly}>
              {readOnly ? '切换到编辑模式' : '切换到只读模式'}
            </Button>
            <Button size="small" color="danger" onClick={clearSelection}>
              清空选择
            </Button>
          </div>

          <div className="bg-gray-100 p-3 rounded">
            <h4 className="font-medium mb-2">当前选择的学生：</h4>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(selectedStudents, null, 2)}
            </pre>
          </div>
        </div>

        <div className="bg-white rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4">组件状态</h3>
          <div className="space-y-2 text-sm">
            <div>模式: {readOnly ? '只读' : '编辑'}</div>
            <div>已选择学生数量: {selectedStudents.length}</div>
            <div>组件状态: 正常</div>
          </div>
        </div>
      </div>
    </div>
  );
}
