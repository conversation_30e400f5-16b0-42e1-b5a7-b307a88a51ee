'use client';

import React, { useState } from 'react';
import { Button } from 'antd-mobile';
import TeacherPicker from '../create/components/widgets/TeacherPicker';

interface Teacher {
  id: string;
  name: string;
  avatar: string;
  type: 'user';
}

export default function TestTeacherPicker() {
  const [selectedTeachers, setSelectedTeachers] = useState<Teacher[]>([]);

  const handleTeachersChange = (teachers: Teacher[]) => {
    setSelectedTeachers(teachers);
    console.log('Selected teachers:', teachers);
  };

  const clearSelection = () => {
    setSelectedTeachers([]);
  };

  return (
    <div className="p-4 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg p-4 mb-4">
        <h1 className="text-lg font-bold mb-4">TeacherPicker 组件测试</h1>

        <div className="mb-4">
          <h2 className="text-base font-semibold mb-2">选择老师：</h2>
          <TeacherPicker
            value={selectedTeachers}
            onChange={handleTeachersChange}
            placeholder="请选择老师"
          />
        </div>

        <div className="mb-4">
          <h2 className="text-base font-semibold mb-2">已选择的老师：</h2>
          {selectedTeachers.length > 0 ? (
            <div className="space-y-2">
              {selectedTeachers.map((teacher) => (
                <div
                  key={teacher.id}
                  className="flex items-center space-x-2 p-2 bg-gray-100 rounded"
                >
                  <span className="font-medium">{teacher.name}</span>
                  <span className="text-sm text-gray-500">
                    ID: {teacher.id}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">暂未选择老师</p>
          )}
        </div>

        <div className="mb-4">
          <h2 className="text-base font-semibold mb-2">只读模式：</h2>
          <TeacherPicker value={selectedTeachers} readOnly={true} />
        </div>

        <div className="flex space-x-2">
          <Button
            color="danger"
            onClick={clearSelection}
            disabled={selectedTeachers.length === 0}
          >
            清空选择
          </Button>
          <Button
            color="primary"
            onClick={() => console.log('当前选择:', selectedTeachers)}
          >
            打印选择结果
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg p-4">
        <h2 className="text-base font-semibold mb-2">JSON 输出：</h2>
        <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
          {JSON.stringify(selectedTeachers, null, 2)}
        </pre>
      </div>
    </div>
  );
}
