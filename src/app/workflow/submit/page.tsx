'use client';

import { <PERSON><PERSON>, SpinLoading, Toast } from 'antd-mobile';
import FormRender, { useForm } from 'form-render-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getApproval, submitApproval } from '@/api/approval';
import { postMessage } from '@/utils';

// import { schema as testSchema } from '../create/components/data';
import address from '../create/components/widgets/Address';
import attachment from '../create/components/widgets/Attachment';
import checkbox from '../create/components/widgets/Checkbox';
import checkboxes from '../create/components/widgets/Checkboxes';
import image from '../create/components/widgets/Image';
import richText from '../create/components/widgets/RichText';
import signature from '../create/components/widgets/Signature';
import studentPicker from '../create/components/widgets/StudentPicker';
import teacherPicker from '../create/components/widgets/TeacherPicker';
import video from '../create/components/widgets/Video';
import ProcessView from './components/ProcessView';

export const dynamic = 'force-dynamic';

export default function Index() {
  const searchParams = useSearchParams();
  const form = useForm();
  const modelId = searchParams?.get('modelId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }

  const [schema, setSchema] = useState<any>(null);
  const [process, setProcess] = useState(null);
  const [dataLoading, setDataLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    if (modelId) {
      setDataLoading(true);
      getApproval(modelId)
        .then((res: any) => {
          setDataLoading(false);
          console.log('🚀 ~ file: page.tsx:28 ~ res:', res);
          if (res.form) {
            setSchema(res.form);
          }
          if (res.process) {
            setProcess(res.process);
          }
        })
        .catch(() => {
          setDataLoading(false);
        });
    }
  }, []);

  const onFinish = (formData: any) => {
    console.log('formData', JSON.stringify(formData, null, 2));
    const data = {
      modelId,
      formData,
      processUsers: {}
    };
    setSubmitLoading(true);
    submitApproval(data)
      .then((res: any) => {
        setSubmitLoading(false);
        Toast.show('提交成功');
        postMessage({ goBack: 2, modelId: res.modelId });
      })
      .catch(() => {
        setSubmitLoading(false);
      });
  };

  const onMount = () => {
    console.log('onMount');
    // setLoading(false);
    setIsMounted(true);
  };

  return (
    <div className="flex flex-col bg-[#F7F9FF]">
      {dataLoading && (
        <div className="flex h-screen w-full items-center justify-center">
          <SpinLoading />
        </div>
      )}
      {!!schema && (
        <FormRender
          schema={schema}
          // requiredMarkStyle="text-required"
          // displayType="column"
          mode="card"
          form={form}
          onFinish={onFinish}
          onMount={onMount}
          widgets={{
            checkbox,
            checkboxes,
            richText,
            signature,
            image,
            video,
            attachment,
            address,
            studentPicker,
            teacherPicker
          }}
        />
      )}

      {process &&
        typeof process === 'object' &&
        Object.keys(process).length > 0 && (
          <div className="mt-4 rounded-md bg-white p-4 text-base">
            <div className="mb-4 text-base">审批流程</div>
            <ProcessView currentData={process} />
          </div>
        )}
      {isMounted && (
        <div className="w-full bg-white px-4 py-2">
          <Button
            block
            type="submit"
            color="primary"
            size="large"
            loading={submitLoading}
            onClick={() => {
              form.submit();
            }}
          >
            提交
          </Button>
        </div>
      )}
    </div>
  );
}
