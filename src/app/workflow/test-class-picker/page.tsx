'use client';

import React, { useState } from 'react';
import ClassPickerWidget from '../create/components/widgets/ClassPicker';

interface ClassInfo {
  id: string;
  name: string;
  gradeName: string;
  fullName: string;
}

export default function TestClassPickerPage() {
  const [singleValue, setSingleValue] = useState<ClassInfo | null>(null);
  const [multipleValue, setMultipleValue] = useState<ClassInfo[]>([]);

  return (
    <div className="p-6 max-w-2xl mx-auto space-y-8">
      <h1 className="text-2xl font-bold text-gray-900">班级选择组件测试</h1>

      {/* 单选模式测试 */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800">单选模式</h2>
        <div className="p-4 border border-gray-200 rounded-lg">
          <ClassPickerWidget
            value={singleValue || undefined}
            onChange={(value) => setSingleValue(value as ClassInfo)}
            placeholder="请选择一个班级"
            multiple={false}
          />
        </div>
        <div className="text-sm text-gray-600">
          <strong>选中值：</strong>
          <pre className="mt-2 p-2 bg-gray-100 rounded text-xs">
            {JSON.stringify(singleValue, null, 2)}
          </pre>
        </div>
      </div>

      {/* 多选模式测试 */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800">多选模式</h2>
        <div className="p-4 border border-gray-200 rounded-lg">
          <ClassPickerWidget
            value={multipleValue}
            onChange={(value) => setMultipleValue(value as ClassInfo[])}
            placeholder="请选择班级"
            multiple={true}
          />
        </div>
        <div className="text-sm text-gray-600">
          <strong>选中值：</strong>
          <pre className="mt-2 p-2 bg-gray-100 rounded text-xs">
            {JSON.stringify(multipleValue, null, 2)}
          </pre>
        </div>
      </div>

      {/* 只读模式测试 */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800">只读模式</h2>

        <div className="space-y-2">
          <h3 className="text-md font-medium text-gray-700">单选只读</h3>
          <div className="p-4 border border-gray-200 rounded-lg">
            <ClassPickerWidget
              value={singleValue || undefined}
              readOnly={true}
              multiple={false}
            />
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-md font-medium text-gray-700">多选只读</h3>
          <div className="p-4 border border-gray-200 rounded-lg">
            <ClassPickerWidget
              value={multipleValue}
              readOnly={true}
              multiple={true}
            />
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-4">
        <button
          type="button"
          onClick={() => setSingleValue(null)}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          清空单选
        </button>
        <button
          type="button"
          onClick={() => setMultipleValue([])}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          清空多选
        </button>
      </div>
    </div>
  );
}
