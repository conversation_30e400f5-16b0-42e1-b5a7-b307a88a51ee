'use client';

/**
 * 审批流组件 - 添加节点组件
 */
import { ActionSheet } from 'antd-mobile';
import type {
  Action,
  ActionSheetShowHandler,
} from 'antd-mobile/es/components/action-sheet';
import React, { useRef } from 'react';

import { PiPlusCircleFill } from '@/components/Icons';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { nanoid } from '@/utils';

import Style from './index.module.scss';

const AddNode = ({ parentNodeData }: any) => {
  const process = useWorkflowStore((state) => state.process);

  const setProcess = useWorkflowStore((state) => state.setProcess);
  const handler = useRef<ActionSheetShowHandler>();

  const actions: Action[] = [
    {
      text: '审批人',
      key: '1',
      onClick: () => addNode(1),
    },
    {
      text: '办理人',
      key: '2',
      onClick: () => addNode(2),
    },
    {
      text: '抄送人',
      key: '3',
      onClick: () => addNode(3),
    },
  ];
  const addNode = (type) => {
    const { id } = parentNodeData;
    const result = loopFn([JSON.parse(JSON.stringify(process))], id, type)[0];
    setProcess(result);
    handler.current?.close();
  };

  // 寻找id
  const loopFn = (arr: any, id: string, type: number) => {
    arr.forEach((item: any) => {
      // 找到了这个节点就开始添加新的子节点
      if (item.id === id) {
        const oldNode = item.children;
        if (type === 1) {
          item.children = {
            id: `node-${nanoid(12)}`,
            name: '审批人',
            type: 'APPROVAL',
            children: oldNode,
            props: {
              assignedType: 'ASSIGN_USER',
              assignedUser: [],
              mode: 'AND',
              sign: false,
              operationPerm: {
                agree: {
                  show: true,
                  alias: '同意',
                },
                refuse: {
                  show: true,
                  alias: '拒绝',
                },
              },
            },
          };
        } else if (type === 2) {
          item.children = {
            id: `node-${nanoid(12)}`,
            name: '办理人',
            type: 'TASK',
            children: oldNode,
            props: {
              assignedType: 'ASSIGN_USER',
              assignedUser: [],
              mode: 'OR',
              sign: false,
              operationPerm: {
                agree: {
                  show: true,
                  alias: '办理',
                },
              },
            },
          };
        } else if (type === 3) {
          item.children = {
            id: `node-${nanoid(12)}`,
            name: '抄送人',
            type: 'CC',
            children: oldNode,
            props: {
              assignedType: 'ASSIGN_USER',
              formPerms: [],
              shouldAdd: false,
              assignedUser: [],
            },
          };
        }
      } else if (item.children) {
        // 当前节点不是目标节点，但是有子节点，则继续遍历子节点
        loopFn([item.children], id, type);
      }
      // 当前节点不是目标节点，但是有条件节点，则继续遍历条件节点
      if (
        item.id !== id &&
        Array.isArray(item.branchs) &&
        item.branchs.length > 0
      ) {
        loopFn(item.branchs, id, type);
      }
    });
    return arr;
  };

  // 点击添加节点
  const onAddNode = () => {
    handler.current = ActionSheet.show({
      actions,
      cancelText: '取消',
    });
  };

  return (
    <div className={Style['add-node-btn-box']}>
      <div className={Style['add-node-btn']}>
        <PiPlusCircleFill
          className="absolute rounded-full bg-white"
          size={32}
          color="#4E78FF"
          onClick={onAddNode}
        />
      </div>
    </div>
  );
};

export default AddNode;
