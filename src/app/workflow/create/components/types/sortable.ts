import type {
  Active,
  CollisionDetection,
  DraggableSyntheticListeners,
  DropAnimation,
  MeasuringConfiguration,
  Modifiers,
  PointerActivationConstraint,
  UniqueIdentifier,
} from '@dnd-kit/core';
import type {
  AnimateLayoutChanges,
  NewIndexGetter,
  SortingStrategy,
} from '@dnd-kit/sortable';
import type { Transform } from '@dnd-kit/utilities';
import type React from 'react';

import type { ItemType } from '../../../types';

// 组件类型常量
export const ItemWidgetType = {
  GROUP: 'group',
  INPUT: 'input',
  TEXTAREA: 'textarea',
  SELECT: 'select',
  RADIO: 'radio',
  CHECKBOX: 'checkbox',
  DATE: 'date',
  TIME: 'time',
  FILE: 'file',
  SIGNATURE: 'signature',
  TEACHER_PICKER: 'teacherPicker',
  VIDEO: 'video',
} as const;

export type ItemWidgetTypeValue =
  (typeof ItemWidgetType)[keyof typeof ItemWidgetType];

// 拖拽配置接口
export interface DragDropConfig {
  activationConstraint?: PointerActivationConstraint;
  collisionDetection?: CollisionDetection;
  measuring?: MeasuringConfiguration;
  modifiers?: Modifiers;
  strategy?: SortingStrategy;
  dropAnimation?: DropAnimation | null;
}

// 样式配置接口
export interface StyleConfig {
  getItemStyles?: (args: {
    id: UniqueIdentifier;
    index: number;
    isSorting: boolean;
    isDragOverlay: boolean;
    overIndex: number;
    isDragging: boolean;
  }) => React.CSSProperties;
  wrapperStyle?: (args: {
    active: Pick<Active, 'id'> | null;
    index: number;
    isDragging: boolean;
    id: UniqueIdentifier;
  }) => React.CSSProperties;
  style?: React.CSSProperties;
}

// 基础 Item Props
export interface BaseItemProps {
  id: UniqueIdentifier;
  value: ItemType;
  index: number;
  disabled?: boolean;
  onRemove?: (id: UniqueIdentifier) => void;
  setWidgetId: (id: string) => void;
}

// 拖拽相关 Props
export interface DragProps {
  dragOverlay?: boolean;
  dragging?: boolean;
  sorting?: boolean;
  fadeIn?: boolean;
  transform?: Transform | null;
  transition?: string | null;
  listeners?: DraggableSyntheticListeners;
  handle?: boolean;
  handleProps?: Record<string, unknown>;
}

// 渲染相关 Props
export interface RenderProps {
  style?: React.CSSProperties;
  wrapperStyle?: React.CSSProperties;
  renderItem?: (args: {
    dragOverlay: boolean;
    dragging: boolean;
    sorting: boolean;
    index: number | undefined;
    fadeIn: boolean;
    listeners: DraggableSyntheticListeners;
    ref: React.Ref<HTMLElement>;
    style: React.CSSProperties | undefined;
    transform: Transform | null;
    transition: string | null;
    value: ItemType;
  }) => React.ReactElement;
}

// 完整的 Item Props
export interface ItemProps extends BaseItemProps, DragProps, RenderProps {}

// Sortable 组件的简化 Props
export interface SortableProps {
  // 数据相关
  forms: ItemType[];
  setForm: (data: ItemType[]) => void;
  removeFormItem: (id: string) => void;
  setWidgetId: (id: string) => void;

  // 配置相关
  dragConfig?: DragDropConfig;
  styleConfig?: StyleConfig;

  // 功能开关
  handle?: boolean;
  removable?: boolean;
  useDragOverlay?: boolean;

  // 高级配置（保留向后兼容）
  animateLayoutChanges?: AnimateLayoutChanges;
  getNewIndex?: NewIndexGetter;
  isDisabled?: (id: UniqueIdentifier) => boolean;
  Container?: React.ComponentType<{ children: React.ReactNode }>;
  renderItem?: RenderProps['renderItem'];
}

// 拖拽状态
export interface DragState {
  activeId: UniqueIdentifier | null;
  activeIndex: number;
}

// Hook 返回值
export interface UseSortableLogicReturn {
  dragState: DragState;
  sensors: ReturnType<typeof import('@dnd-kit/core').useSensors>;
  handleDragStart: (event: { active: Active }) => void;
  handleDragEnd: (event: { over: { id: UniqueIdentifier } | null }) => void;
  handleDragCancel: () => void;
}

// 分组相关类型
export interface GroupItemData extends ItemType {
  widget: typeof ItemWidgetType.GROUP;
  properties?: Record<string, ItemType>;
}

// 表单项类型守卫
export const isGroupItem = (item: ItemType): item is GroupItemData => {
  return item.widget === ItemWidgetType.GROUP;
};
