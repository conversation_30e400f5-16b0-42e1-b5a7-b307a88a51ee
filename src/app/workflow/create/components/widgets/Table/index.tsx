'use client';

import React, { useState, useCallback } from 'react';
import { omit } from 'lodash-es';
import { Button, Input, Dialog, Toast } from 'antd-mobile';
import { Plus, Trash2, Edit3 } from 'lucide-react';

// 表格组件的类型定义
interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
}

interface TableRow {
  [key: string]: any;
  _id: string; // 内部使用的唯一标识
}

interface TableWidgetProps {
  value?: TableRow[];
  onChange?: (value: TableRow[]) => void;
  readOnly?: boolean;
  schema?: {
    properties?: {
      columns?: TableColumn[];
    };
  };
  addons?: Record<string, unknown>;
  [key: string]: unknown;
}

// 生成唯一ID
const generateId = () =>
  `row_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// 表格行编辑弹窗组件
const RowEditModal: React.FC<{
  visible: boolean;
  columns: TableColumn[];
  rowData: TableRow | null;
  onConfirm: (data: TableRow) => void;
  onCancel: () => void;
}> = ({ visible, columns, rowData, onConfirm, onCancel }) => {
  const [formData, setFormData] = useState<Record<string, any>>({});

  React.useEffect(() => {
    if (visible && rowData) {
      setFormData({ ...rowData });
    } else if (visible && !rowData) {
      // 新增行时初始化空数据
      const initialData: Record<string, any> = { _id: generateId() };
      for (const col of columns) {
        initialData[col.dataIndex] = '';
      }
      setFormData(initialData);
    }
  }, [visible, rowData, columns]);

  const handleInputChange = (key: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [key]: value
    }));
  };

  const handleConfirm = () => {
    // 验证必填字段
    const hasEmptyFields = columns.some(
      (col) =>
        !formData[col.dataIndex] ||
        formData[col.dataIndex].toString().trim() === ''
    );

    if (hasEmptyFields) {
      Toast.show({
        content: '请填写完整信息',
        position: 'center'
      });
      return;
    }

    onConfirm(formData as TableRow);
  };

  return (
    <Dialog
      visible={visible}
      title={rowData ? '编辑行' : '新增行'}
      content={
        <div className="space-y-4 py-4">
          {columns.map((column) => (
            <div key={column.key} className="space-y-2">
              <label
                className="block text-sm font-medium text-gray-700"
                htmlFor={column.key}
              >
                {column.title}
              </label>
              <Input
                value={formData[column.dataIndex] || ''}
                onChange={(value) => handleInputChange(column.dataIndex, value)}
                placeholder={`请输入${column.title}`}
              />
            </div>
          ))}
        </div>
      }
      actions={[
        {
          key: 'cancel',
          text: '取消',
          onClick: onCancel
        },
        {
          key: 'confirm',
          text: '确定',
          bold: true,
          onClick: handleConfirm
        }
      ]}
    />
  );
};

// 主表格组件
export default function TableWidget(props: TableWidgetProps) {
  const { onChange, ...restProps } = props;
  const {
    readOnly = false,
    value = [],
    schema,
    ...rest
  } = omit(restProps, ['addons']);

  // 从 schema 中获取列配置，如果没有则使用默认配置
  const columns: TableColumn[] = schema?.properties?.columns || [
    { key: 'col1', title: '列1', dataIndex: 'col1' },
    { key: 'col2', title: '列2', dataIndex: 'col2' },
    { key: 'col3', title: '列3', dataIndex: 'col3' }
  ];

  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingRow, setEditingRow] = useState<TableRow | null>(null);

  // 添加新行
  const handleAddRow = useCallback(() => {
    setEditingRow(null);
    setEditModalVisible(true);
  }, []);

  // 编辑行
  const handleEditRow = useCallback((row: TableRow) => {
    setEditingRow(row);
    setEditModalVisible(true);
  }, []);

  // 删除行
  const handleDeleteRow = useCallback(
    (rowId: string) => {
      Dialog.confirm({
        title: '确认删除',
        content: '确定要删除这一行数据吗？',
        onConfirm: () => {
          const newValue = value.filter((row) => row._id !== rowId);
          onChange?.(newValue);
        }
      });
    },
    [value, onChange]
  );

  // 确认编辑/新增
  const handleRowConfirm = useCallback(
    (rowData: TableRow) => {
      let newValue: TableRow[];

      if (editingRow) {
        // 编辑现有行
        newValue = value.map((row) =>
          row._id === editingRow._id ? rowData : row
        );
      } else {
        // 新增行
        newValue = [...value, rowData];
      }

      onChange?.(newValue);
      setEditModalVisible(false);
      setEditingRow(null);
    },
    [editingRow, value, onChange]
  );

  // 取消编辑
  const handleEditCancel = useCallback(() => {
    setEditModalVisible(false);
    setEditingRow(null);
  }, []);

  // 只读模式渲染
  if (readOnly) {
    if (!value || value.length === 0) {
      return <div className="text-gray-400 text-center py-4">暂无数据</div>;
    }

    return (
      <div className="w-full overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700"
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {value.map((row, index) => (
              <tr key={row._id || index} className="hover:bg-gray-50">
                {columns.map((column) => (
                  <td
                    key={column.key}
                    className="border border-gray-300 px-3 py-2 text-sm text-gray-900"
                  >
                    {row[column.dataIndex] || '-'}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  }

  // 编辑模式渲染
  return (
    <div className="w-full space-y-4">
      {/* 表格 */}
      <div className="w-full overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700"
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
              <th className="border border-gray-300 px-3 py-2 text-center text-sm font-medium text-gray-700 w-20">
                操作
              </th>
            </tr>
          </thead>
          <tbody>
            {value.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + 1}
                  className="border border-gray-300 px-3 py-8 text-center text-gray-400"
                >
                  暂无数据，点击下方按钮添加
                </td>
              </tr>
            ) : (
              value.map((row, index) => (
                <tr key={row._id || index} className="hover:bg-gray-50">
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className="border border-gray-300 px-3 py-2 text-sm text-gray-900"
                    >
                      {row[column.dataIndex] || '-'}
                    </td>
                  ))}
                  <td className="border border-gray-300 px-3 py-2 text-center">
                    <div className="flex justify-center space-x-2">
                      <button
                        type="button"
                        onClick={() => handleEditRow(row)}
                        className="text-blue-500 hover:text-blue-700"
                      >
                        <Edit3 size={16} />
                      </button>
                      <button
                        type="button"
                        onClick={() => handleDeleteRow(row._id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* 添加按钮 */}
      <Button
        color="primary"
        fill="outline"
        onClick={handleAddRow}
        className="w-full"
      >
        <Plus size={16} className="mr-2" />
        添加行
      </Button>

      {/* 编辑弹窗 */}
      <RowEditModal
        visible={editModalVisible}
        columns={columns}
        rowData={editingRow}
        onConfirm={handleRowConfirm}
        onCancel={handleEditCancel}
      />
    </div>
  );
}
