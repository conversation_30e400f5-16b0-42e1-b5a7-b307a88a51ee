import type { DraggableSyntheticListeners } from '@dnd-kit/core';
import type { Transform } from '@dnd-kit/utilities';
import clsx from 'clsx';

import React, { useEffect } from 'react';

import { useWorkflowStore } from '@/store/useWorkflowStore';
import { Handle, Remove } from './components';
import styles from './Item.module.scss';

export interface Props {
  dragOverlay?: boolean;
  color?: string;
  disabled?: boolean;
  dragging?: boolean;
  handle?: boolean;
  handleProps?: any;
  height?: number;
  index?: number;
  fadeIn?: boolean;
  transform?: Transform | null;
  listeners?: DraggableSyntheticListeners;
  sorting?: boolean;
  style?: React.CSSProperties;
  transition?: string | null;
  wrapperStyle?: React.CSSProperties;
  value: any;
  onRemove?(): void;
  setWidgetId: (id: string) => void;
  renderItem?(args: {
    dragOverlay: boolean;
    dragging: boolean;
    sorting: boolean;
    index: number | undefined;
    fadeIn: boolean;
    listeners: DraggableSyntheticListeners;
    ref: React.Ref<HTMLElement>;
    style: React.CSSProperties | undefined;
    transform: Props['transform'];
    transition: Props['transition'];
    value: Props['value'];
  }): React.ReactElement;
}

export const Item = React.memo(
  React.forwardRef<HTMLLIElement, Props>(
    (
      {
        color,
        dragOverlay,
        dragging,
        disabled,
        fadeIn,
        handle,
        handleProps,
        height,
        index,
        listeners,
        onRemove,
        renderItem,
        sorting,
        style,
        transition,
        transform,
        value,
        wrapperStyle,
        setWidgetId,
        ...props
      },
      ref
    ) => {
      const removeGroupChildItem = useWorkflowStore(
        (state) => state.removeGroupChildItem
      );

      useEffect(() => {
        if (!dragOverlay) {
          return;
        }
        if (typeof document !== 'undefined') {
          document.body.style.cursor = 'grabbing';

          return () => {
            document.body.style.cursor = '';
          };
        }
      }, [dragOverlay]);

      // 如果是分组类型，直接在这里渲染分组和子组件
      if (value.widget === 'group') {
        const childItems = value.properties
          ? Object.keys(value.properties).map((key) => ({
              ...value.properties[key],
              id: key,
            }))
          : [];

        return (
          <li
            className={clsx(
              styles.Wrapper,
              fadeIn && styles.fadeIn,
              sorting && styles.sorting,
              dragOverlay && styles.dragOverlay
            )}
            ref={ref}
            style={
              {
                ...wrapperStyle,
                transition: [transition, wrapperStyle?.transition]
                  .filter(Boolean)
                  .join(', '),
                '--translate-x': transform
                  ? `${Math.round(transform.x)}px`
                  : undefined,
                '--translate-y': transform
                  ? `${Math.round(transform.y)}px`
                  : undefined,
                '--scale-x': transform?.scaleX
                  ? `${transform.scaleX}`
                  : undefined,
                '--scale-y': transform?.scaleY
                  ? `${transform.scaleY}`
                  : undefined,
                '--index': index,
                '--color': color,
              } as React.CSSProperties
            }
            {...(handle ? undefined : listeners)}
            {...props}
          >
            <div className={clsx(styles.Item, 'group-container')}>
              {/* 分组标题 */}
              <div className="mb-2 flex w-full justify-between border-slate-100 border-b pb-2">
                <div className="flex-1 text-base text-gray-900">
                  {value.title || '分组'}
                </div>
                <div className="w-7 text-gray-400">
                  <span className="rounded bg-blue-100 px-2 py-1 text-blue-600 text-xs">
                    分组
                  </span>
                </div>
              </div>

              {/* 分组内容区域 */}
              <div className="rounded-lg border-2 border-gray-200 border-dashed bg-gray-50 p-3">
                {childItems.length > 0 ? (
                  <div className="space-y-2">
                    {childItems.map((childItem) => (
                      <Item
                        dragging={false}
                        dragOverlay={false}
                        fadeIn={false}
                        handle={false}
                        key={childItem.id}
                        onRemove={() =>
                          removeGroupChildItem(value.id, childItem.id)
                        }
                        setWidgetId={setWidgetId}
                        sorting={false}
                        value={childItem}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="py-4 text-center text-gray-500 text-sm">
                    暂无控件，点击下方按钮添加
                  </div>
                )}

                {/* 添加控件按钮 */}
                <button
                  className="mt-3 flex w-full items-center justify-center rounded border border-blue-300 border-dashed py-2 text-blue-600 hover:bg-blue-50"
                  onClick={() => {
                    // TODO: 打开控件选择器，添加到分组内
                    console.log('添加控件到分组:', value.id);
                  }}
                  type="button"
                >
                  <span className="mr-1 text-lg">+</span>
                  添加控件
                </button>
              </div>

              {/* 分组操作按钮 */}
              <div className="mt-2 flex items-center justify-between">
                <div className="flex items-center">
                  {onRemove ? (
                    <Remove className={styles.Remove} onClick={onRemove} />
                  ) : null}
                  <span className="ml-2 text-sm text-stone-500">删除分组</span>
                </div>
                <div className="flex items-center">
                  <button
                    className="primary-color ml-3 text-base"
                    onClick={() => setWidgetId(value.id)}
                    type="button"
                  >
                    分组设置
                  </button>
                  <div className={clsx(styles.Actions, 'ml-3')}>
                    {handle ? <Handle {...handleProps} {...listeners} /> : null}
                  </div>
                </div>
              </div>
            </div>
          </li>
        );
      }

      return renderItem ? (
        renderItem({
          dragOverlay: Boolean(dragOverlay),
          dragging: Boolean(dragging),
          sorting: Boolean(sorting),
          index,
          fadeIn: Boolean(fadeIn),
          listeners,
          ref,
          style,
          transform,
          transition,
          value,
        })
      ) : (
        <li
          className={clsx(
            styles.Wrapper,
            fadeIn && styles.fadeIn,
            sorting && styles.sorting,
            dragOverlay && styles.dragOverlay
          )}
          ref={ref}
          style={
            {
              ...wrapperStyle,
              transition: [transition, wrapperStyle?.transition]
                .filter(Boolean)
                .join(', '),
              '--translate-x': transform
                ? `${Math.round(transform.x)}px`
                : undefined,
              '--translate-y': transform
                ? `${Math.round(transform.y)}px`
                : undefined,
              '--scale-x': transform?.scaleX
                ? `${transform.scaleX}`
                : undefined,
              '--scale-y': transform?.scaleY
                ? `${transform.scaleY}`
                : undefined,
              '--index': index,
              '--color': color,
            } as React.CSSProperties
          }
        >
          <div
            className={clsx(
              styles.Item,
              dragging && styles.dragging,
              handle && styles.withHandle,
              dragOverlay && styles.dragOverlay,
              disabled && styles.disabled,
              color && styles.color
            )}
            data-cypress="draggable-item"
            style={style}
            {...(handle ? undefined : listeners)}
            {...props}
            tabIndex={handle ? undefined : 0}
          >
            <div className="mb-2 flex w-full justify-between border-slate-100 border-b pb-2">
              <div className="flex-1 text-base text-gray-900">
                {value.title}
              </div>
              <div className="w-7 text-gray-400">
                {value.required ? '必填' : null}
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {onRemove ? (
                  <Remove className={styles.Remove} onClick={onRemove} />
                ) : null}
                <span className="ml-2 text-sm text-stone-500">删除</span>
              </div>
              <div className="flex items-center">
                <button
                  className="primary-color ml-3 text-base"
                  onClick={() => {
                    // router.push(`/workflow/create/widgetSetting/${value.id}`, {
                    //   scroll: false,
                    // });
                    setWidgetId(value.id);
                  }}
                  type="button"
                >
                  控件设置
                </button>
                <div className={clsx(styles.Actions, 'ml-3')}>
                  {handle ? <Handle {...handleProps} {...listeners} /> : null}
                </div>
              </div>
            </div>
          </div>
        </li>
      );
    }
  )
);
