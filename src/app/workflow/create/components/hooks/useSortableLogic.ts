import type { Active, UniqueIdentifier } from '@dnd-kit/core';
import { Mouse<PERSON><PERSON>or, TouchSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { useEffect, useRef, useState } from 'react';

import type { ItemType } from '../../../types';
import type { DragDropConfig, UseSortableLogicReturn } from '../types/sortable';

interface UseSortableLogicProps {
  forms: ItemType[];
  setForm: (data: ItemType[]) => void;
  config?: DragDropConfig;
}

export function useSortableLogic({
  forms,
  setForm,
  config = {},
}: UseSortableLogicProps): UseSortableLogicReturn {
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const isFirstAnnouncement = useRef(true);

  // 默认配置
  const { activationConstraint } = config;

  // 传感器配置
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint,
    }),
    useSensor(TouchSensor, {
      activationConstraint,
    })
  );

  // 获取索引的辅助函数
  const getIndex = (id: UniqueIdentifier) =>
    forms.findIndex((item) => item.id === id);

  const activeIndex = activeId ? getIndex(activeId) : -1;

  // 重置首次公告标志
  useEffect(() => {
    if (!activeId) {
      isFirstAnnouncement.current = true;
    }
  }, [activeId]);

  // 拖拽开始处理
  const handleDragStart = ({ active }: { active: Active }) => {
    if (!active) {
      return;
    }
    setActiveId(active.id);
  };

  // 拖拽结束处理
  const handleDragEnd = ({
    over,
  }: {
    over: { id: UniqueIdentifier } | null;
  }) => {
    setActiveId(null);

    if (over) {
      const overIndex = getIndex(over.id);
      if (activeIndex !== overIndex) {
        const reorderedItems = arrayMove(forms, activeIndex, overIndex);
        setForm(reorderedItems);
      }
    }
  };

  // 拖拽取消处理
  const handleDragCancel = () => {
    setActiveId(null);
  };

  return {
    dragState: {
      activeId,
      activeIndex,
    },
    sensors,
    handleDragStart,
    handleDragEnd,
    handleDragCancel,
  };
}
