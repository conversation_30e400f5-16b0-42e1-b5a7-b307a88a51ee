'use client';

import { Button } from 'antd-mobile';
import FormRender, { useForm } from 'form-render-mobile';
import React, { useState } from 'react';

import { schema as testSchema } from '../create/components/data';
import address from '../create/components/widgets/Address';
import attachment from '../create/components/widgets/Attachment';
import checkbox from '../create/components/widgets/Checkbox';
import checkboxes from '../create/components/widgets/Checkboxes';
import image from '../create/components/widgets/Image';
import richText from '../create/components/widgets/RichText';
import signature from '../create/components/widgets/Signature';
import studentPicker from '../create/components/widgets/StudentPicker';
import teacherPicker from '../create/components/widgets/TeacherPicker';
import classPicker from '../create/components/widgets/ClassPicker';
import video from '../create/components/widgets/Video';

export default function Index() {
  const form = useForm();

  const [isMounted, setIsMounted] = useState(false);

  const onFinish = (formData: any) => {
    console.log('getSchema', form.getSchema());
    console.log('formData', JSON.stringify(formData, null, 2));
  };

  const onMount = () => {
    console.log('onMount');
    // setLoading(false);
    setIsMounted(true);

    form.setValues({
      input: 'erwerew',
      textArea: 'werewrew',
      inputNumber: '34343',
      amountNumber: '3343',
      phoneNumber: '13233334444',
      signature:
        'data:image/png;base64,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',
      image: [
        {
          thumbnailUrl:
            'https://unicorn-media.ancda.com/test/workflow/2023-12-08/Uoyv4A35.jpg?x-image-process=image/resize,m_fill,w_300,h_300,limit_0/format,jpg',
          url: 'https://unicorn-media.ancda.com/test/workflow/2023-12-08/Uoyv4A35.jpg',
          type: 'image'
        }
      ],
      video: [
        {
          thumbnailUrl:
            'https://unicorn-media.ancda.com/test/workflow/2023-12-08/VltPA3zx.mp4?x-workflow-graph-name=video-thumbnail',
          url: 'https://unicorn-media.ancda.com/test/workflow/2023-12-08/VltPA3zx.mp4',
          type: 'video'
        }
      ],
      attachment: [
        {
          url: 'https://unicorn-media.ancda.com/test/workflow/2023-12-09/x9zFVt1q.jpg',
          name: '51bV5sFSOuS._AC_SL1500_.jpg',
          type: 'image/jpeg',
          size: 70327
        },
        {
          url: 'https://unicorn-media.ancda.com/test/workflow/2023-12-09/EJvj8p1B.jpg',
          name: 'c22f9aa5-b899-4bde-8039-b7b2992a9e7b.jpg',
          type: 'image/jpeg',
          size: 8606715
        }
      ],
      picker: ['1'],
      checkboxes: ['1'],
      date: '2023-12',
      dateTime: '2023-12-08 03:34',
      slider: [0, 35],
      switch: true,
      address: {
        area: [
          {
            value: '130000000000',
            label: '河北省'
          },
          {
            value: '130200000000',
            label: '唐山市'
          },
          {
            value: '130203000000',
            label: '路北区'
          }
        ],
        detail: 'ererer'
      },
      rate: 4,
      selector: ['2'],
      selector2: ['2', '1'],
      stepper: 3,
      cascader: [1, 2],
      radio: '1',
      checkbox: '1',
      group1: {
        input: '233',
        input2: '444'
      },
      group2: {
        input1: '3434',
        input2: '3434'
      },
      card: {
        input1: '34',
        input2: '343',
        input3: '5535'
      },
      list: [
        {
          input1: '343',
          input2: '3434',
          input3: '3434'
        }
      ],
      studentPicker: [
        {
          studentId: 'student001',
          studentName: '张小明',
          avatar:
            'https://unicorn-media.ancda.com/production/app/avatar/app_avatar_student_girl.png',
          gender: 1,
          classId: 'class001'
        },
        {
          studentId: 'student002',
          studentName: '李小红',
          avatar:
            'https://unicorn-media.ancda.com/production/app/avatar/app_avatar_student_girl.png',
          gender: 2,
          classId: 'class001'
        }
      ],
      teacherPicker: [
        {
          id: 'teacher001',
          name: '王老师',
          avatar:
            'https://unicorn-media.ancda.com/production/app/avatar/app_avatar_student_girl.png',
          type: 'user'
        },
        {
          id: 'teacher002',
          name: '赵老师',
          avatar:
            'https://unicorn-media.ancda.com/production/app/avatar/app_avatar_student_girl.png',
          type: 'user'
        }
      ]
    });
  };

  return (
    <div className="flex flex-col bg-[#F7F9FF]">
      <FormRender
        schema={testSchema}
        // requiredMarkStyle="text-required"
        // displayType="column"
        mode="card"
        form={form}
        onFinish={onFinish}
        onMount={onMount}
        footer={
          <Button block type="submit" color="primary" size="large">
            提交
          </Button>
        }
        widgets={{
          checkbox,
          checkboxes,
          richText,
          signature,
          image,
          video,
          attachment,
          address,
          studentPicker,
          teacherPicker,
          classPicker
        }}
      />
    </div>
  );
}
