'use client';

import 'rc-slider/assets/index.css';
import 'react-modern-drawer/dist/index.css';
import 'react-responsive-carousel/lib/styles/carousel.css';

import axios from 'axios';
import clsx from 'clsx';
import Konva from 'konva';
import Head from 'next/head';
import Img from 'next/image';
import { useSearchParams } from 'next/navigation';
import Slider from 'rc-slider';
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import Drawer from 'react-modern-drawer';
import { Carousel } from 'react-responsive-carousel';

// import { mockData } from "./data";
import { handleRender } from '@/app/album/components/TooltipSlider';
import { apiHost } from '@/constant/env';
import { useAudioPlayerStore } from '@/store/useAudioPlayerStore';
import styles from '@/styles/album.module.css';
import {
  addClickEvent,
  debounce,
  getMessage,
  isBigScreen,
  postMessage,
} from '@/utils';
import { generateCatalogue } from '@/utils/album';

export const dynamic = 'force-dynamic';

if (typeof window !== 'undefined') {
  if (
    window.navigator.userAgent.indexOf('Android') > -1 ||
    window.navigator.userAgent.indexOf('linux') > -1
  ) {
    Konva.pixelRatio = Number((window.devicePixelRatio * 0.55).toFixed(2));
  } else {
    Konva.pixelRatio = Number((window.devicePixelRatio * 0.65).toFixed(2));
  }
}

const sceneWidth = 2480;
const sceneHeight = 3508;

const App = (): JSX.Element => {
  const searchParams = useSearchParams();
  const archivesId = searchParams?.get('archivesId') || null;
  const index = searchParams?.get('index') || 0;
  const pIdx = Number(index);
  const [loading, setLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(true);
  const [title, setTitle] = useState('');
  const [pageIndex, setPageIndex] = useState(pIdx); // 页面索引
  const [sliderIndex, setSliderIndex] = useState(pIdx); // 底部滑动条索引

  const [isOpenDrawer, setIsOpenDrawer] = useState(false);
  const toggleDrawer = () => {
    setIsOpenDrawer((prevState) => !prevState);
  };
  const refreshIndex = useRef(0); // 记住需要刷新的页面
  const stagesRef: any = useRef([]);
  const stagesData: any = useRef([]);
  const audioRef: any = useRef(null);
  const audioPlayingRef: any = useRef(false);
  const isShowModifyModalRef: any = useRef(false);
  const pageContainer = useRef<any>([]);
  const catalogRef = useRef<any[]>([]);
  const turnbookRef = useRef<any>(null);
  const [change, setChange] = useState(1);

  const carouselRef: any = useRef(false);

  const sliderIndexRef = useRef<any>(null);
  const previousPages = useRef<number>(0);
  const setSliderIndexRef = (val: number) => {
    sliderIndexRef.current = val;
    setSliderIndex(val);
  };

  const { setStatus } = useAudioPlayerStore((state: any) => ({
    setStatus: state.setStatus,
  }));

  useEffect(() => {
    getMessage(onMessage);
  }, []);

  useEffect(() => {
    refreshIndex.current = pageIndex;
    renderPage(pageIndex);
  }, [pageIndex]);

  useLayoutEffect(() => {
    if (archivesId) {
      getData(pageIndex);
    }
    // 初始化非首页要重置页面动画
    if (pageIndex > 0) {
      onAfterChangeSlider(pageIndex);
    }
  }, [archivesId]);

  const getData = (initIndex: number) => {
    axios
      .get(`${apiHost}/v1/archives/growthRecord/archivesDetail`, {
        params: { archivesId },
      })
      .then((response: any) => {
        const data = response.data.archivesInfo;
        if (!data) {
          setIsOpen(false);
          isHistoryArchives();
          return;
        }
        if (data.status !== 1) {
          // 历史档案禁止编辑
          isHistoryArchives();
        }
        const cover = data.cover || '';
        const backCover = data.backCover || '';
        const pageData = data.profile || '';
        const name = data.name || '';
        const studentName = data.studentName || '';
        const deptName = data.deptName || '';
        if (name) {
          setTitle(`${name}的成长档案`);
        }
        // if (!pageData) {
        //   setLoading(false);
        //   setIsOpen(false);
        //   return;
        // }
        const pageArray: any[] = [];
        pageArray.push(initCover(cover, studentName, deptName)); // 添加封面
        try {
          const data = JSON.parse(pageData);
          data.forEach((item: any) => {
            if (item) {
              // 将主题分隔成单个页面
              const stageData = JSON.parse(item);
              if (Array.isArray(stageData)) {
                let editType = 0; // 可编辑类型： 1，老师，2，家长
                stageData.map((pageItem, index) => {
                  // 只有封面返回了可编辑权限值，赋值此主题的到其他页面
                  if (index === 0) {
                    // 0为封面
                    if (pageItem.attrs?.staffEditable === 1) {
                      editType = 1;
                    }
                    if (pageItem.attrs?.studentEditable === 1) {
                      editType = 2;
                    }
                  }
                  pageItem.attrs.editType = editType;

                  pageArray.push({
                    ...pageItem,
                    isInit: false, // isInit用来判断是否需要重新渲染
                  });
                });
              } else if (stageData.className !== 'Shape') {
                // 有些页面是空的，不渲染
                pageArray.push({
                  ...stageData,
                  isInit: false,
                });
              }
            }
          });
        } catch (error) {
          console.log('error', error);
        }
        pageArray.push(initCover(backCover, '封底')); // 添加封底
        console.log(
          '🚀 ~ file: preview.tsx:183 ~ pageArray:',
          JSON.stringify(pageArray),
        );
        initCatalog(pageArray); // 初始化目录

        // pageArray = mockData;
        stagesData.current = pageArray;

        if (pageArray.length > 0) {
          if (previousPages.current === 0) {
            previousPages.current = pageArray.length;
          }
          const deletePages = previousPages.current - pageArray.length;
          console.log('🚀 ~ file: preview.tsx:192 ~ deletePages:', deletePages);
          if (deletePages > 0) {
            initIndex -= deletePages;
          }
          setTimeout(() => {
            // 延迟加载为了等待字体包下载完
            setLoading(false);
            if (initIndex !== undefined) {
              // 初始化两页
              initStage(initIndex);
              if (deletePages > 0) {
                setPageIndex(initIndex);
                setSliderIndex(initIndex);
                turnbookRef.current.skipPage(initIndex);
              }
              previousPages.current = pageArray.length;
              setTimeout(() => {
                initStage(initIndex + 1);
              }, 200);
            }
          }, 200);
        }
        // setChange(stagesData.current.length);
      })
      .catch(() => {
        console.log('get data error');
      });
  };

  // 生成目录
  const initCatalog = async (pageArray: any[]) => {
    const catalogue = await generateCatalogue(pageArray);
    if (Array.isArray(catalogue) && catalogue.length) {
      catalogRef.current = catalogue;
    }
  };

  // 添加封面数据
  const initCover = (cover: string) => ({
    attrs: {
      width: 2480,
      height: 3508,
      listening: true,
      themeId: 0,
      tmpType: 0,
    },
    className: 'Stage',
    children: [
      {
        attrs: {
          listening: true,
        },
        className: 'Layer',
        children: [
          {
            attrs: {
              source: cover,
              scaleX: 1,
              scaleY: 1,
            },
            className: 'Image',
          },
        ],
      },
    ],
    isInit: false,
  });

  // 渲染页面，i为页面索引
  const initStage = (i: number) => {
    if (i < 0 || i >= stagesData.current.length) return;
    const data = stagesData.current[i];
    if (!data) {
      return;
    }
    if (data.isInit) {
      return;
    }
    // listening为true才可以添加事件
    data.attrs.listening = true;
    data.children[0].attrs.listening = true;
    const stage = Konva.Node.create(data, stagesRef.current[i]);
    // 重置画布大小为手机大小
    function fitStageIntoParentContainer() {
      const containerWidth = stagesRef.current[i].offsetWidth;
      const scale = Number((containerWidth / sceneWidth).toFixed(4));
      stage.width(sceneWidth * scale);
      stage.height(sceneHeight * scale);
      stage.scale({ x: scale, y: scale });
    }

    fitStageIntoParentContainer();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', fitStageIntoParentContainer);
    }

    // 加载图片
    stage.find('Image').forEach((node: any) => {
      const imageUrl = node.getAttr('source');
      const recordId = node.getAttr('recordId');
      if (!stagesData.current[i].attrs.recordId && recordId) {
        // 此类型为头部为单个图片
        // 页面id放在recordId数组里
        stagesData.current[i].attrs.recordId = [recordId];
      }
      const img = new Image();
      img.onload = () => {
        node.image(img);
        stage.batchDraw();
      };
      img.src = imageUrl;
      // img.src = node.getAttr("source") + "?x-image-process=image/quality,q_80";

      // 音频和视频二维码添加事件
      const url = node.getAttr('url');
      const mediaType = node.getAttr('mediaType');
      if (url && mediaType) {
        addClickEvent(node, true, () => {
          if (mediaType === 'video') {
            viewMedia(url, 1);
            setStatus(2);
          } else if (mediaType === 'audio') {
            if (audioPlayingRef.current) {
              return;
            }
            const imageObj = new Image();
            imageObj.src =
              'https://edu-media.ancda.com/prod/archives/album/common/music.png';
            imageObj.onload = () => {
              node.image(imageObj);
              node.setAttrs({
                x: 1710 + 90,
                y: 90,
                offset: {
                  x: 90,
                  y: 90,
                },
              });
              stage.batchDraw();
            };

            audioRef.current = new Audio(url);
            audioPlayingRef.current = true;
            audioRef.current.play();
            setStatus(2);
            // 动画
            const angularSpeed = 100;
            const anim = new Konva.Animation((frame: any) => {
              const angularDiff = (angularSpeed * frame.timeDiff) / 1000;
              node.rotate(angularDiff);
            }, node.getLayer());

            anim.start();

            audioRef.current.addEventListener('ended', () => {
              console.log('play end');
              node.image(img);
              anim.stop();
              setStatus(1);
              audioPlayingRef.current = false;
              node.setAttrs({
                rotation: 0,
              });
            });
            // viewMedia(url, 2);
          }
        });
      }
    });

    // 一个页面两条内容需要添加点击事件用来区分编辑
    const group = stage.find('Group');
    // stage 不存在recordId属性，但页面元素包含recordId
    if (Array.isArray(group)) {
      group.forEach((item) => {
        const recordId = item.attrs?.recordId || '';
        if (recordId) {
          if (stagesData.current[i].attrs.recordId) {
            // 页面id放在recordId数组，去重
            if (
              Array.isArray(stagesData.current[i].attrs.recordId) &&
              !stagesData.current[i].attrs.recordId.includes(recordId)
            ) {
              stagesData.current[i].attrs.recordId.push(recordId);
            }
          } else {
            stagesData.current[i].attrs.recordId = [recordId];
          }
        }
      });
    }

    // 头像处理成圆角
    const tmpType = stage.getAttr('tmpType');
    if (tmpType === 3) {
      stage.find('Group')[1].setAttrs({
        clipFunc(ctx: any) {
          ctx.arc(496 / 2, 248, 248, 0, Math.PI * 2, false);
        },
      });
    }

    // 分页数字更新
    group.forEach((node: any) => {
      const isPageNode = node.attrs.type && node.attrs.type === 'pageNum';
      if (isPageNode) {
        node.children.map((val: any) => {
          const className = val.getClassName();
          if (className === 'Text') {
            val.setAttr('text', String(i));
          }
        });
      }
    });
    // carouselRef.current.itemsRef[i].style.pointerEvents='auto'
    // if (i > 0) {
    // carouselRef.current.itemsRef[i-1].style.pointerEvents='auto'
    // }
    if (!stagesData.current[i].attrs.recordId) {
      stagesData.current[i].attrs.recordId = [];
    }
    stagesData.current[i].stage = stage;
    stagesData.current[i].isInit = true; // 标记渲染完毕，防止重复渲染
  };

  // 修改内容在标题上显示一个遮罩提示框
  const showModifyModal = () => {
    if (isShowModifyModalRef.current) {
      isShowModifyModalRef.current = false;
      hideModifyModal();
      return;
    }
    // const index = refreshIndex.current
    const index = sliderIndexRef.current;
    const { stage } = stagesData.current[index];
    console.log(
      'stagesData.current[index].attrs',
      stagesData.current[index].attrs,
    );
    if (!stage) {
      return;
    }
    const group = stage.find('Group');
    // 有多条记录时才做处理
    if (
      Array.isArray(stagesData.current[index].attrs.recordId) &&
      Array.isArray(group)
    ) {
      group.forEach((item) => {
        const recordId = item.attrs?.recordId || '';
        const content = item.attrs?.type || '';
        if (recordId && content === 'content') {
          // 添加一个提示框
          const width = 1980;
          const height = 280;
          const maskGroup = new Konva.Group({
            x: 0,
            y: 0,
            width,
            height,
            offset: {
              x: 30,
              y: 45,
            },
            type: 'mask',
          });
          const rect = new Konva.Rect({
            width,
            height,
            fill: 'rgba(255,255,0,0.7)',
            stroke: 'black',
            strokeWidth: 0,
            cornerRadius: 40,
          });
          const simpleText = new Konva.Text({
            x: width / 2 - 220,
            y: height / 2 - 40,
            text: '点击修改此条',
            fontSize: 90,
            fill: 'black',
          });
          maskGroup.add(rect);
          maskGroup.add(simpleText);

          item.add(maskGroup);
          isShowModifyModalRef.current = true;
          // 添加点击事件
          addClickEvent(item, false, () => {
            modify(recordId);
            // maskGroup.hide();
            // 点击隐藏掉所有的提示框
            group.forEach((item) => {
              const recordId = item.attrs?.recordId || '';
              const content = item.attrs?.type || '';
              if (recordId && content === 'content') {
                const maskNode = item.find('Group');
                maskNode.forEach((node: any) => {
                  if (node.attrs.type === 'mask') {
                    node.destroy();
                  }
                });
              }
            });
          });
        }
      });
    }
  };

  // 隐藏修改提示遮罩
  const hideModifyModal = () => {
    const index = sliderIndexRef.current;
    const { stage } = stagesData.current[index];
    if (!stage) {
      return;
    }
    const group = stage.find('Group');
    // 有多条记录时才做处理
    if (
      Array.isArray(stagesData.current[index].attrs.recordId) &&
      Array.isArray(group)
    ) {
      group.forEach((item) => {
        const recordId = item.attrs?.recordId || '';
        const content = item.attrs?.type || '';
        if (recordId && content === 'content') {
          const maskNode = item.find('Group');
          maskNode.forEach((node: any) => {
            if (node.attrs.type === 'mask') {
              node.destroy();
            }
          });
        }
      });
      isShowModifyModalRef.current = false;
    }
  };

  // 通知RN修改内容
  const modify = (id: number) => {
    console.log('modify', id);

    const postMessageData = {
      recordId: id,
      modify: true,
    };
    postMessage(postMessageData);
  };

  // 通知RN是历史档案
  const isHistoryArchives = () => {
    console.log('历史档案');
    const postMessageData = {
      isHistory: 1,
    };
    postMessage(postMessageData);
  };

  // 通知RN预览视频
  const viewMedia = (url: number, type: number) => {
    console.log('通知RN预览视频', url);

    const postMessageData = {
      url,
      viewMedia: type,
    };
    postMessage(postMessageData);
  };

  // 发送页面信息给RN
  const setMessage = debounce((value: number) => {
    if (value === undefined) {
      return;
    }
    try {
      const stageInfo = stagesData.current[value];
      if (stageInfo.attrs) {
        const postMessageData: any = {
          themeId: stageInfo.attrs.themeId,
          tmpType: stageInfo.attrs.tmpType,
        };
        if (stageInfo.attrs.recordId) {
          postMessageData.recordId = stageInfo.attrs.recordId;
        }
        if (stageInfo.attrs.editType) {
          postMessageData.editType = stageInfo.attrs.editType;
        }
        postMessage(postMessageData);
      }
    } catch (error) {
      console.log('postMessage', error);
    }
  }, 500);

  // 获取到RN的通知
  const onMessage = useCallback((event: any) => {
    console.log('获取到RN的通知data: ', event);
    try {
      const data = JSON.parse(event.data);
      console.log('sliderIndexRef.current: ', sliderIndexRef.current);
      if (data.refresh === true) {
        getData(refreshIndex.current);
      }
      if (data.modify === true) {
        showModifyModal();
      }
      if (data.openCatalog === true) {
        toggleDrawer();
      }
    } catch (error) {
      console.log('onMessage', error);
    }
  }, []);

  const onChangePage = useCallback((value: number) => {
    console.log('onChangePagevalue: ', value);
    // 隐藏修改蒙版
    if (isShowModifyModalRef.current) {
      hideModifyModal();
    }
    setPageIndex(value);
    setSliderIndexRef(value);
    setMessage(value);
  }, []);

  const renderPage = useCallback((value: number) => {
    initStage(value - 1);
    initStage(value + 1);
  }, []);

  // 底部滑动条
  const onChangeSlider = useCallback((value: any) => {
    setSliderIndexRef(value);
    setPageIndex(value);
  }, []);

  // 底部滑动条
  const onAfterChangeSlider = debounce((value: any) => {
    // 底部滑动条滚时重置参数
    // turnbookRef.current.skipPage(value);
    initStage(value);
    setPageIndex(value);
    setMessage(value);
  }, 200);

  if (!isOpen) {
    return <div className={styles.notExisting}>档案不存在</div>;
  }

  const startTurnpages = (index: number) => {
    onChangePage(index);
  };
  const endTurnpages = (index: number) => {
    console.log('endTurnpages', index);
    setSliderIndexRef(index);
    setMessage(index);
  };

  return (
    <div className={styles.page}>
      <Head>
        <title>{title}</title>
      </Head>
      <Carousel
        ref={carouselRef}
        showArrows={false}
        showStatus={false}
        showIndicators={false}
        showThumbs={false}
        // autoFocus={true}
        transitionTime={0}
        preventMovementUntilSwipeScrollTolerance={true}
        swipeScrollTolerance={0}
        onChange={onChangePage}
        selectedItem={pageIndex}
      >
        {stagesData.current.map((_: any, index: number) => {
          return (
            <div
              key={index.toString()}
              className={styles.pageContainer}
              id="stage-container"
            >
              <div
                ref={(ref) => {
                  stagesRef.current[index] = ref;
                }}
              />
            </div>
          );
        })}
      </Carousel>
      {stagesData.current.length > 0 && (
        <div className={styles.sliderContainer}>
          <Slider
            min={0}
            max={stagesData.current.length - 1}
            defaultValue={0}
            value={sliderIndex}
            handleRender={handleRender}
            onChange={onChangeSlider}
            onChangeComplete={onAfterChangeSlider}
            railStyle={{ backgroundColor: '#999', height: 5 }}
            trackStyle={{ backgroundColor: 'white', height: 5 }}
            handleStyle={{
              borderWidth: 0,
              height: 20,
              width: 20,
              opacity: 1,
              backgroundColor: '#FFF',
              marginTop: -7,
              boxShadow: 'none',
            }}
          />
          <div className={styles.pageInfo}>
            共{stagesData.current.length - 2}页
          </div>
        </div>
      )}
      <Drawer open={isOpenDrawer} onClose={toggleDrawer} direction="left">
        <div className={styles.catalog}>
          {catalogRef.current.map((item, index) => {
            const currentPage = item.page;
            const nextPage = () => {
              let pageValue;
              if (index !== catalogRef.current.length - 1) {
                // 排除index + 1不存在的情况
                pageValue = catalogRef.current[index + 1].page - 1;
              }
              if (pageValue === undefined) {
                return '';
              }
              return currentPage !== pageValue ? `-${pageValue}` : '';
            };
            const page = `${currentPage}${nextPage()}`;
            const isActive =
              item.page <= pageIndex &&
              (pageIndex < catalogRef.current[index + 1]?.page ||
                index === catalogRef.current.length - 1);
            return (
              <div
                key={item.page}
                className={clsx({
                  [styles.catalogItem]: true,
                  [styles.catalogItemActive]: isActive,
                })}
                onClick={() => {
                  onAfterChangeSlider(item.page);
                  toggleDrawer();
                }}
              >
                {item.page > 0 && index < catalogRef.current.length && (
                  <div className={styles.catalogItemDesc}>第 {page} 页：</div>
                )}
                <div className={styles.catalogItemTitle}>{item.title}</div>
                {isActive && (
                  <Img
                    src="https://edu-media.ancda.com/prod/archives/album/common/catelog-active.png"
                    width={16}
                    height={16}
                    alt=""
                  />
                )}
              </div>
            );
          })}
        </div>
      </Drawer>
      {/* <button onClick={toggleDrawer}>Show</button> */}
    </div>
  );
};
export default App;
