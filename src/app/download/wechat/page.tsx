import { headers } from 'next/headers';
import Image from 'next/image';

import { isPalmBaby } from '@/lib/utils';

import WechatDown from '../components/WechatDown';
import styles from '../styles.module.css';

const data = [
  {
    platform: 'Android',
    data: [
      {
        name: '家长端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.parents',
        qrcode: '/images/qrcode/android-parents-app-download.png',
        downloadUrl32:
          'https://unicorn-media.ancda.com/production/app/apk/ZhangXinZhiXiao_parents-release-32.apk',
      },
      {
        name: '园丁端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.teacher',
        qrcode: '/images/qrcode/android-teacher-app-download.png',
      },
    ],
  },
  {
    platform: 'iOS',
    data: [
      {
        name: '家长端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.parents',
        qrcode: '/images/qrcode/ios-parents-app-download.png',
      },
      {
        name: '园丁端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.teacher',
        qrcode: '/images/qrcode/ios-teacher-app-download.png',
      },
    ],
  },
];

const babyData = [
  {
    platform: 'Android',
    data: [
      {
        name: '家长端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents',
        qrcode: '/images/qrcode/android-parents-baby-download.png',
        downloadUrl32:
          'https://unicorn-media.ancda.com/production/app/apk/babyParents-release-32.apk',
      },
      {
        name: '园丁端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents.teacher',
        qrcode: '/images/qrcode/android-teacher-baby-download.png',
      },
    ],
  },
  {
    platform: 'iOS',
    data: [
      {
        name: '家长端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents',
        qrcode: '/images/qrcode/ios-parents-baby-download.png',
      },
      {
        name: '园丁端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents.teacher',
        qrcode: '/images/qrcode/ios-teacher-baby-download.png',
      },
    ],
  },
];

export async function generateMetadata() {
  const headersList = headers();
  const hostname = headersList.get('host') || '';
  const isPalmBabyApp = isPalmBaby(hostname);

  return {
    title: 'APP下载指引',
    description: `${
      isPalmBabyApp ? '掌心宝贝' : '掌心智校'
    }APP是一款多功能、综合性的创新解决方案，专为幼儿园打造，旨在提供更优质的管理和服务方式。我们以先进技术和用户友好的界面为基础，构建了一个紧密而便捷的校园平台，为教师、学生家长创造无缝互动体验。我们不断努力创新，推动学前教育的高质量发展，感谢您的支持，期待您亲身体验！`,
  };
}

export default function DownloadPage() {
  const headersList = headers();
  const userAgent = headersList.get('user-agent') || '';
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  return (
    <main className={`${styles.container} `}>
      <section className={`${styles.bg} relative`}>
        <Image
          src='/images/download/header.png'
          alt=''
          width={0}
          height={0}
          sizes='100vw'
          className='absolute right-0 top-0 z-0 w-screen object-cover'
        />
        <WechatDown data={data} babyData={babyData} isIOS={isIOS} />
      </section>
    </main>
  );
}
